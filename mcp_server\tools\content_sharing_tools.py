"""
Content sharing and curation tools for TutorX-MCP.

This module provides MCP tools for uploading, sharing, and curating
educational resources within the collaborative learning platform.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

from ..mcp_instance import mcp
from ..models.collaboration_models import SharedResource, ResourceType
from ..storage.memory_store import MemoryStore

# Initialize storage
content_store = MemoryStore(persistence_file="content_sharing_data.json")


@mcp.tool()
async def upload_shared_resource(
    uploader_id: str,
    title: str,
    description: str,
    resource_type: str,
    file_url: str,
    file_name: str,
    file_size: int,
    subject: str = None,
    grade_level: str = None,
    topics: List[str] = None,
    tags: List[str] = None,
    is_public: bool = True,
    thumbnail_url: str = None
) -> Dict[str, Any]:
    """
    Upload and share an educational resource.
    
    Args:
        uploader_id: ID of the user uploading the resource
        title: Resource title
        description: Resource description
        resource_type: Type of resource (document, image, video, etc.)
        file_url: URL where the file is stored
        file_name: Original file name
        file_size: File size in bytes
        subject: Subject area (optional)
        grade_level: Target grade level (optional)
        topics: List of topics covered (optional)
        tags: List of tags for categorization (optional)
        is_public: Whether the resource is publicly accessible
        thumbnail_url: URL of thumbnail image (optional)
    
    Returns:
        Resource upload result
    """
    try:
        # Verify uploader exists
        uploader = content_store.get_user(uploader_id)
        if not uploader:
            return {
                "success": False,
                "error": "Uploader not found"
            }
        
        # Create shared resource
        resource_id = str(uuid.uuid4())
        resource = SharedResource(
            resource_id=resource_id,
            title=title,
            description=description,
            uploader_id=uploader_id,
            resource_type=ResourceType(resource_type),
            file_url=file_url,
            file_name=file_name,
            file_size=file_size,
            thumbnail_url=thumbnail_url,
            subject=subject,
            grade_level=grade_level,
            topics=topics or [],
            tags=tags or [],
            is_public=is_public
        )
        
        # Save resource
        if content_store.save_shared_resource(resource):
            return {
                "success": True,
                "resource": resource.to_dict()
            }
        else:
            return {
                "success": False,
                "error": "Failed to save resource"
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error uploading resource: {str(e)}"
        }


@mcp.tool()
async def get_shared_resource(
    resource_id: str,
    user_id: str = None
) -> Dict[str, Any]:
    """
    Get details of a shared resource.
    
    Args:
        resource_id: Resource ID
        user_id: ID of the user requesting the resource (for access control)
    
    Returns:
        Resource details
    """
    try:
        resource = content_store.get_shared_resource(resource_id)
        if not resource:
            return {
                "success": False,
                "error": "Resource not found"
            }
        
        # Check access permissions
        if not resource.is_public and user_id:
            # Check if user has access (uploader, shared with user, or shared with user's groups)
            if (resource.uploader_id != user_id and 
                user_id not in resource.shared_with_users):
                
                # Check if user is in any shared groups
                user_groups = content_store.get_user_study_groups(user_id)
                user_group_ids = [group.group_id for group in user_groups]
                
                if not any(gid in resource.shared_with_groups for gid in user_group_ids):
                    return {
                        "success": False,
                        "error": "Access denied to this resource"
                    }
        
        # Increment view count if user is provided
        if user_id:
            content_store.increment_resource_view(resource_id)
        
        return {
            "success": True,
            "resource": resource.to_dict()
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error getting resource: {str(e)}"
        }


@mcp.tool()
async def search_shared_resources(
    query: str = None,
    resource_type: str = None,
    subject: str = None,
    grade_level: str = None,
    tags: List[str] = None,
    uploader_id: str = None,
    is_public: bool = None,
    limit: int = 20
) -> Dict[str, Any]:
    """
    Search for shared educational resources.
    
    Args:
        query: Search query for title/description
        resource_type: Filter by resource type
        subject: Filter by subject
        grade_level: Filter by grade level
        tags: Filter by tags
        uploader_id: Filter by uploader
        is_public: Filter by public/private status
        limit: Maximum number of results
    
    Returns:
        List of matching resources
    """
    try:
        resources = content_store.search_resources(
            query=query,
            resource_type=resource_type,
            subject=subject,
            grade_level=grade_level,
            tags=tags,
            is_public=is_public,
            limit=limit
        )
        
        # Filter by uploader if specified
        if uploader_id:
            resources = [r for r in resources if r.uploader_id == uploader_id]
        
        return {
            "success": True,
            "resources": [resource.to_dict() for resource in resources]
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error searching resources: {str(e)}"
        }


@mcp.tool()
async def get_user_resources(user_id: str) -> Dict[str, Any]:
    """
    Get all resources uploaded by a user.
    
    Args:
        user_id: User ID
    
    Returns:
        List of user's uploaded resources
    """
    try:
        resources = content_store.get_user_resources(user_id)
        return {
            "success": True,
            "resources": [resource.to_dict() for resource in resources]
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error getting user resources: {str(e)}"
        }


@mcp.tool()
async def rate_resource(
    resource_id: str,
    user_id: str,
    rating: int,
    review: str = ""
) -> Dict[str, Any]:
    """
    Rate and review a shared resource.
    
    Args:
        resource_id: Resource ID
        user_id: ID of the user rating the resource
        rating: Rating from 1 to 5
        review: Optional review text
    
    Returns:
        Rating result
    """
    try:
        # Validate rating
        if not 1 <= rating <= 5:
            return {
                "success": False,
                "error": "Rating must be between 1 and 5"
            }
        
        # Get resource
        resource = content_store.get_shared_resource(resource_id)
        if not resource:
            return {
                "success": False,
                "error": "Resource not found"
            }
        
        # Verify user exists
        user = content_store.get_user(user_id)
        if not user:
            return {
                "success": False,
                "error": "User not found"
            }
        
        # Add rating
        if resource.add_rating(user_id, rating, review):
            # Save updated resource
            content_store.save_shared_resource(resource)
            
            return {
                "success": True,
                "message": "Rating added successfully",
                "average_rating": resource.average_rating
            }
        else:
            return {
                "success": False,
                "error": "Failed to add rating"
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error rating resource: {str(e)}"
        }


@mcp.tool()
async def download_resource(
    resource_id: str,
    user_id: str
) -> Dict[str, Any]:
    """
    Record a resource download and return download information.
    
    Args:
        resource_id: Resource ID
        user_id: ID of the user downloading the resource
    
    Returns:
        Download information
    """
    try:
        # Get resource
        resource = content_store.get_shared_resource(resource_id)
        if not resource:
            return {
                "success": False,
                "error": "Resource not found"
            }
        
        # Check access permissions (same logic as get_shared_resource)
        if not resource.is_public:
            if (resource.uploader_id != user_id and 
                user_id not in resource.shared_with_users):
                
                user_groups = content_store.get_user_study_groups(user_id)
                user_group_ids = [group.group_id for group in user_groups]
                
                if not any(gid in resource.shared_with_groups for gid in user_group_ids):
                    return {
                        "success": False,
                        "error": "Access denied to this resource"
                    }
        
        # Increment download count
        content_store.increment_resource_download(resource_id)
        
        return {
            "success": True,
            "download_url": resource.file_url,
            "file_name": resource.file_name,
            "file_size": resource.file_size,
            "message": "Download recorded successfully"
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error downloading resource: {str(e)}"
        }


@mcp.tool()
async def share_resource_with_group(
    resource_id: str,
    group_id: str,
    user_id: str
) -> Dict[str, Any]:
    """
    Share a resource with a study group.
    
    Args:
        resource_id: Resource ID
        group_id: Study group ID
        user_id: ID of the user sharing the resource
    
    Returns:
        Sharing result
    """
    try:
        # Get resource
        resource = content_store.get_shared_resource(resource_id)
        if not resource:
            return {
                "success": False,
                "error": "Resource not found"
            }
        
        # Check if user can share this resource (must be uploader or have access)
        if resource.uploader_id != user_id:
            return {
                "success": False,
                "error": "Only the uploader can share this resource"
            }
        
        # Verify group exists and user is a member
        group = content_store.get_study_group(group_id)
        if not group:
            return {
                "success": False,
                "error": "Study group not found"
            }
        
        if not group.is_member(user_id):
            return {
                "success": False,
                "error": "User is not a member of this group"
            }
        
        # Add group to shared list
        if group_id not in resource.shared_with_groups:
            resource.shared_with_groups.append(group_id)
            content_store.save_shared_resource(resource)
        
        return {
            "success": True,
            "message": f"Resource shared with group '{group.name}'"
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error sharing resource: {str(e)}"
        }
