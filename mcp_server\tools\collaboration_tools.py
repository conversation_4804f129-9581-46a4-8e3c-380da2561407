"""
Collaboration tools for TutorX-MCP.

This module provides MCP tools for peer-to-peer learning,
study groups, chat systems, and collaborative sessions.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

from ..mcp_instance import mcp
from ..models.collaboration_models import (
    User, UserRole, StudyGroup, ChatMessage, MessageType,
    CollaborativeSession, SessionType
)
from ..storage.memory_store import MemoryStore

# Initialize storage
collaboration_store = MemoryStore(persistence_file="collaboration_data.json")


@mcp.tool()
async def create_user(
    username: str,
    email: str,
    role: str = "student",
    display_name: str = None,
    grade_level: str = None,
    subjects_of_interest: List[str] = None
) -> Dict[str, Any]:
    """
    Create a new user account.
    
    Args:
        username: Unique username
        email: User's email address
        role: User role (student, educator, admin, moderator)
        display_name: Display name (optional)
        grade_level: Grade level for students
        subjects_of_interest: List of subjects the user is interested in
    
    Returns:
        User creation result with user details
    """
    try:
        # Check if username already exists
        existing_user = collaboration_store.get_user_by_username(username)
        if existing_user:
            return {
                "success": False,
                "error": "Username already exists"
            }
        
        # Create new user
        user_id = str(uuid.uuid4())
        user = User(
            user_id=user_id,
            username=username,
            email=email,
            role=UserRole(role),
            display_name=display_name,
            grade_level=grade_level,
            subjects_of_interest=subjects_of_interest or []
        )
        
        # Save user
        if collaboration_store.save_user(user):
            return {
                "success": True,
                "user": user.to_dict()
            }
        else:
            return {
                "success": False,
                "error": "Failed to save user"
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error creating user: {str(e)}"
        }


@mcp.tool()
async def get_user_profile(user_id: str) -> Dict[str, Any]:
    """
    Get user profile information.
    
    Args:
        user_id: User ID
    
    Returns:
        User profile data
    """
    try:
        user = collaboration_store.get_user(user_id)
        if user:
            return {
                "success": True,
                "user": user.to_dict()
            }
        else:
            return {
                "success": False,
                "error": "User not found"
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error getting user profile: {str(e)}"
        }


@mcp.tool()
async def search_users(
    query: str,
    role: str = None,
    limit: int = 20
) -> Dict[str, Any]:
    """
    Search for users by username or display name.
    
    Args:
        query: Search query
        role: Filter by user role (optional)
        limit: Maximum number of results
    
    Returns:
        List of matching users
    """
    try:
        if role:
            users = collaboration_store.get_users_by_role(role)
            # Filter by query
            query_lower = query.lower()
            filtered_users = [
                user for user in users
                if (query_lower in user.username.lower() or
                    (user.display_name and query_lower in user.display_name.lower()))
            ][:limit]
        else:
            filtered_users = collaboration_store.search_users(query, limit)
        
        return {
            "success": True,
            "users": [user.to_dict() for user in filtered_users]
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error searching users: {str(e)}"
        }


@mcp.tool()
async def create_study_group(
    creator_id: str,
    name: str,
    description: str,
    subject_focus: str = None,
    grade_level: str = None,
    is_public: bool = True,
    max_members: int = 20
) -> Dict[str, Any]:
    """
    Create a new study group.
    
    Args:
        creator_id: ID of the user creating the group
        name: Group name
        description: Group description
        subject_focus: Subject the group focuses on
        grade_level: Target grade level
        is_public: Whether the group is public
        max_members: Maximum number of members
    
    Returns:
        Study group creation result
    """
    try:
        # Verify creator exists
        creator = collaboration_store.get_user(creator_id)
        if not creator:
            return {
                "success": False,
                "error": "Creator not found"
            }
        
        # Create study group
        group_id = str(uuid.uuid4())
        study_group = StudyGroup(
            group_id=group_id,
            name=name,
            description=description,
            creator_id=creator_id,
            subject_focus=subject_focus,
            grade_level=grade_level,
            is_public=is_public,
            max_members=max_members,
            members=[creator_id],  # Creator is automatically a member
            moderators=[creator_id]  # Creator is automatically a moderator
        )
        
        # Save study group
        if collaboration_store.save_study_group(study_group):
            return {
                "success": True,
                "study_group": study_group.to_dict()
            }
        else:
            return {
                "success": False,
                "error": "Failed to save study group"
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error creating study group: {str(e)}"
        }


@mcp.tool()
async def join_study_group(
    group_id: str,
    user_id: str
) -> Dict[str, Any]:
    """
    Join a study group.
    
    Args:
        group_id: Study group ID
        user_id: User ID
    
    Returns:
        Join result
    """
    try:
        # Verify user exists
        user = collaboration_store.get_user(user_id)
        if not user:
            return {
                "success": False,
                "error": "User not found"
            }
        
        # Verify group exists
        group = collaboration_store.get_study_group(group_id)
        if not group:
            return {
                "success": False,
                "error": "Study group not found"
            }
        
        # Check if user is banned
        if user_id in group.banned_users:
            return {
                "success": False,
                "error": "User is banned from this group"
            }
        
        # Add user to group
        if collaboration_store.add_user_to_group(group_id, user_id):
            return {
                "success": True,
                "message": f"Successfully joined group '{group.name}'"
            }
        else:
            return {
                "success": False,
                "error": "Failed to join group (may be full or user already a member)"
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error joining study group: {str(e)}"
        }


@mcp.tool()
async def leave_study_group(
    group_id: str,
    user_id: str
) -> Dict[str, Any]:
    """
    Leave a study group.
    
    Args:
        group_id: Study group ID
        user_id: User ID
    
    Returns:
        Leave result
    """
    try:
        # Remove user from group
        if collaboration_store.remove_user_from_group(group_id, user_id):
            return {
                "success": True,
                "message": "Successfully left the study group"
            }
        else:
            return {
                "success": False,
                "error": "Failed to leave group (user may not be a member)"
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error leaving study group: {str(e)}"
        }


@mcp.tool()
async def get_user_study_groups(user_id: str) -> Dict[str, Any]:
    """
    Get all study groups for a user.
    
    Args:
        user_id: User ID
    
    Returns:
        List of user's study groups
    """
    try:
        groups = collaboration_store.get_user_study_groups(user_id)
        return {
            "success": True,
            "study_groups": [group.to_dict() for group in groups]
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error getting user study groups: {str(e)}"
        }


@mcp.tool()
async def search_study_groups(
    query: str = None,
    subject: str = None,
    grade_level: str = None,
    is_public: bool = None,
    limit: int = 20
) -> Dict[str, Any]:
    """
    Search for study groups.
    
    Args:
        query: Search query for name/description
        subject: Filter by subject
        grade_level: Filter by grade level
        is_public: Filter by public/private status
        limit: Maximum number of results
    
    Returns:
        List of matching study groups
    """
    try:
        groups = collaboration_store.search_study_groups(
            query=query,
            subject=subject,
            grade_level=grade_level,
            is_public=is_public,
            limit=limit
        )
        
        return {
            "success": True,
            "study_groups": [group.to_dict() for group in groups]
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error searching study groups: {str(e)}"
        }


@mcp.tool()
async def send_group_message(
    sender_id: str,
    group_id: str,
    content: str,
    message_type: str = "text",
    reply_to_message_id: str = None
) -> Dict[str, Any]:
    """
    Send a message to a study group.

    Args:
        sender_id: ID of the user sending the message
        group_id: Study group ID
        content: Message content
        message_type: Type of message (text, image, file, etc.)
        reply_to_message_id: ID of message being replied to (optional)

    Returns:
        Message sending result
    """
    try:
        # Verify sender exists
        sender = collaboration_store.get_user(sender_id)
        if not sender:
            return {
                "success": False,
                "error": "Sender not found"
            }

        # Verify group exists and user is a member
        group = collaboration_store.get_study_group(group_id)
        if not group:
            return {
                "success": False,
                "error": "Study group not found"
            }

        if not group.is_member(sender_id):
            return {
                "success": False,
                "error": "User is not a member of this group"
            }

        # Create message
        message_id = str(uuid.uuid4())
        message = ChatMessage(
            message_id=message_id,
            sender_id=sender_id,
            group_id=group_id,
            content=content,
            message_type=MessageType(message_type),
            reply_to_message_id=reply_to_message_id
        )

        # Save message
        if collaboration_store.save_chat_message(message):
            return {
                "success": True,
                "message": message.to_dict()
            }
        else:
            return {
                "success": False,
                "error": "Failed to save message"
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"Error sending group message: {str(e)}"
        }


@mcp.tool()
async def send_direct_message(
    sender_id: str,
    recipient_id: str,
    content: str,
    message_type: str = "text"
) -> Dict[str, Any]:
    """
    Send a direct message to another user.

    Args:
        sender_id: ID of the user sending the message
        recipient_id: ID of the recipient
        content: Message content
        message_type: Type of message (text, image, file, etc.)

    Returns:
        Message sending result
    """
    try:
        # Verify sender and recipient exist
        sender = collaboration_store.get_user(sender_id)
        recipient = collaboration_store.get_user(recipient_id)

        if not sender:
            return {
                "success": False,
                "error": "Sender not found"
            }

        if not recipient:
            return {
                "success": False,
                "error": "Recipient not found"
            }

        # Check if recipient allows messages
        if not recipient.allow_messages:
            return {
                "success": False,
                "error": "Recipient does not allow direct messages"
            }

        # Create message
        message_id = str(uuid.uuid4())
        message = ChatMessage(
            message_id=message_id,
            sender_id=sender_id,
            recipient_id=recipient_id,
            content=content,
            message_type=MessageType(message_type)
        )

        # Save message
        if collaboration_store.save_chat_message(message):
            return {
                "success": True,
                "message": message.to_dict()
            }
        else:
            return {
                "success": False,
                "error": "Failed to save message"
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"Error sending direct message: {str(e)}"
        }


@mcp.tool()
async def get_group_messages(
    group_id: str,
    user_id: str,
    limit: int = 50,
    before_message_id: str = None
) -> Dict[str, Any]:
    """
    Get messages from a study group.

    Args:
        group_id: Study group ID
        user_id: ID of the user requesting messages
        limit: Maximum number of messages to return
        before_message_id: Get messages before this message ID (for pagination)

    Returns:
        List of group messages
    """
    try:
        # Verify group exists and user is a member
        group = collaboration_store.get_study_group(group_id)
        if not group:
            return {
                "success": False,
                "error": "Study group not found"
            }

        if not group.is_member(user_id):
            return {
                "success": False,
                "error": "User is not a member of this group"
            }

        # Get messages
        messages = collaboration_store.get_group_messages(
            group_id, limit, before_message_id
        )

        return {
            "success": True,
            "messages": [message.to_dict() for message in messages],
            "group_name": group.name
        }

    except Exception as e:
        return {
            "success": False,
            "error": f"Error getting group messages: {str(e)}"
        }


@mcp.tool()
async def get_direct_messages(
    user1_id: str,
    user2_id: str,
    requesting_user_id: str,
    limit: int = 50
) -> Dict[str, Any]:
    """
    Get direct messages between two users.

    Args:
        user1_id: First user ID
        user2_id: Second user ID
        requesting_user_id: ID of the user requesting the messages
        limit: Maximum number of messages to return

    Returns:
        List of direct messages
    """
    try:
        # Verify requesting user is one of the participants
        if requesting_user_id not in [user1_id, user2_id]:
            return {
                "success": False,
                "error": "User can only access their own conversations"
            }

        # Get messages
        messages = collaboration_store.get_direct_messages(user1_id, user2_id, limit)

        return {
            "success": True,
            "messages": [message.to_dict() for message in messages]
        }

    except Exception as e:
        return {
            "success": False,
            "error": f"Error getting direct messages: {str(e)}"
        }
