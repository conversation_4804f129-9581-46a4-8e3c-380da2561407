"""
Learning session data models for TutorX-MCP.

This module defines data structures for managing learning sessions,
session states, and session events.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import json


class SessionState(Enum):
    """States of a learning session."""
    CREATED = "created"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ABANDONED = "abandoned"
    ERROR = "error"


class SessionEvent(Enum):
    """Types of events that can occur in a learning session."""
    SESSION_START = "session_start"
    SESSION_END = "session_end"
    SESSION_PAUSE = "session_pause"
    SESSION_RESUME = "session_resume"
    QUESTION_ANSWERED = "question_answered"
    HINT_REQUESTED = "hint_requested"
    CONCEPT_MASTERED = "concept_mastered"
    DIFFICULTY_ADJUSTED = "difficulty_adjusted"
    INTERACTION = "interaction"
    IDLE_DETECTED = "idle_detected"
    ERROR_OCCURRED = "error_occurred"


@dataclass
class LearningEvent:
    """Individual event within a learning session."""
    event_id: str
    event_type: SessionEvent
    timestamp: datetime = field(default_factory=datetime.utcnow)
    
    # Event data
    data: Dict[str, Any] = field(default_factory=dict)
    
    # Context information
    concept_id: Optional[str] = None
    question_id: Optional[str] = None
    user_input: Optional[str] = None
    system_response: Optional[str] = None
    
    # Performance data
    response_time: Optional[float] = None
    is_correct: Optional[bool] = None
    difficulty_level: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'timestamp': self.timestamp.isoformat(),
            'data': self.data,
            'concept_id': self.concept_id,
            'question_id': self.question_id,
            'user_input': self.user_input,
            'system_response': self.system_response,
            'response_time': self.response_time,
            'is_correct': self.is_correct,
            'difficulty_level': self.difficulty_level
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LearningEvent':
        """Create LearningEvent from dictionary."""
        timestamp = datetime.fromisoformat(data['timestamp']) if data.get('timestamp') else datetime.utcnow()
        
        return cls(
            event_id=data['event_id'],
            event_type=SessionEvent(data['event_type']),
            timestamp=timestamp,
            data=data.get('data', {}),
            concept_id=data.get('concept_id'),
            question_id=data.get('question_id'),
            user_input=data.get('user_input'),
            system_response=data.get('system_response'),
            response_time=data.get('response_time'),
            is_correct=data.get('is_correct'),
            difficulty_level=data.get('difficulty_level')
        )


@dataclass
class LearningSession:
    """Learning session for tracking student progress and interactions."""
    session_id: str
    student_id: str
    
    # Session configuration
    session_type: str = "general"  # general, quiz, lesson, adaptive, etc.
    learning_objectives: List[str] = field(default_factory=list)
    target_concepts: List[str] = field(default_factory=list)
    
    # Session state
    state: SessionState = SessionState.CREATED
    current_concept: Optional[str] = None
    current_difficulty: float = 0.5
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    last_activity: Optional[datetime] = None
    
    # Session data
    events: List[LearningEvent] = field(default_factory=list)
    context_data: Dict[str, Any] = field(default_factory=dict)
    
    # Progress tracking
    concepts_attempted: List[str] = field(default_factory=list)
    concepts_mastered: List[str] = field(default_factory=list)
    questions_answered: int = 0
    correct_answers: int = 0
    hints_used: int = 0
    
    # Performance metrics
    total_time: int = 0  # seconds
    active_time: int = 0  # seconds (excluding idle time)
    idle_time: int = 0  # seconds
    average_response_time: float = 0.0
    
    # Adaptive learning data
    difficulty_adjustments: List[Dict[str, Any]] = field(default_factory=list)
    learning_path: List[str] = field(default_factory=list)
    
    def start_session(self):
        """Start the learning session."""
        self.state = SessionState.ACTIVE
        self.started_at = datetime.utcnow()
        self.last_activity = datetime.utcnow()
        
        # Add session start event
        self.add_event(SessionEvent.SESSION_START, {
            'session_type': self.session_type,
            'learning_objectives': self.learning_objectives,
            'target_concepts': self.target_concepts
        })
    
    def end_session(self, reason: str = "completed"):
        """End the learning session."""
        self.ended_at = datetime.utcnow()
        self.state = SessionState.COMPLETED if reason == "completed" else SessionState.ABANDONED
        
        # Calculate total time
        if self.started_at:
            self.total_time = int((self.ended_at - self.started_at).total_seconds())
            self.active_time = self.total_time - self.idle_time
        
        # Add session end event
        self.add_event(SessionEvent.SESSION_END, {
            'reason': reason,
            'total_time': self.total_time,
            'questions_answered': self.questions_answered,
            'correct_answers': self.correct_answers,
            'concepts_attempted': len(self.concepts_attempted),
            'concepts_mastered': len(self.concepts_mastered)
        })
    
    def pause_session(self):
        """Pause the learning session."""
        self.state = SessionState.PAUSED
        self.add_event(SessionEvent.SESSION_PAUSE, {})
    
    def resume_session(self):
        """Resume the learning session."""
        self.state = SessionState.ACTIVE
        self.last_activity = datetime.utcnow()
        self.add_event(SessionEvent.SESSION_RESUME, {})
    
    def add_event(self, event_type: SessionEvent, data: Dict[str, Any] = None, **kwargs):
        """Add an event to the session."""
        event_id = f"{self.session_id}_{len(self.events)}"
        
        event = LearningEvent(
            event_id=event_id,
            event_type=event_type,
            data=data or {},
            **kwargs
        )
        
        self.events.append(event)
        self.last_activity = datetime.utcnow()
        
        # Update session metrics based on event type
        if event_type == SessionEvent.QUESTION_ANSWERED:
            self.questions_answered += 1
            if event.is_correct:
                self.correct_answers += 1
            
            # Update average response time
            if event.response_time:
                total_response_time = self.average_response_time * (self.questions_answered - 1)
                self.average_response_time = (total_response_time + event.response_time) / self.questions_answered
        
        elif event_type == SessionEvent.HINT_REQUESTED:
            self.hints_used += 1
        
        elif event_type == SessionEvent.CONCEPT_MASTERED:
            if event.concept_id and event.concept_id not in self.concepts_mastered:
                self.concepts_mastered.append(event.concept_id)
    
    def update_difficulty(self, new_difficulty: float, reason: str = "adaptive"):
        """Update the current difficulty level."""
        old_difficulty = self.current_difficulty
        self.current_difficulty = new_difficulty
        
        adjustment = {
            'timestamp': datetime.utcnow().isoformat(),
            'old_difficulty': old_difficulty,
            'new_difficulty': new_difficulty,
            'reason': reason
        }
        self.difficulty_adjustments.append(adjustment)
        
        self.add_event(SessionEvent.DIFFICULTY_ADJUSTED, adjustment)
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get a summary of the session."""
        duration = 0
        if self.started_at and self.ended_at:
            duration = int((self.ended_at - self.started_at).total_seconds())
        elif self.started_at:
            duration = int((datetime.utcnow() - self.started_at).total_seconds())
        
        accuracy = 0.0
        if self.questions_answered > 0:
            accuracy = self.correct_answers / self.questions_answered
        
        return {
            'session_id': self.session_id,
            'student_id': self.student_id,
            'state': self.state.value,
            'duration': duration,
            'questions_answered': self.questions_answered,
            'correct_answers': self.correct_answers,
            'accuracy': accuracy,
            'hints_used': self.hints_used,
            'concepts_attempted': len(self.concepts_attempted),
            'concepts_mastered': len(self.concepts_mastered),
            'current_difficulty': self.current_difficulty,
            'average_response_time': self.average_response_time
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'session_id': self.session_id,
            'student_id': self.student_id,
            'session_type': self.session_type,
            'learning_objectives': self.learning_objectives,
            'target_concepts': self.target_concepts,
            'state': self.state.value,
            'current_concept': self.current_concept,
            'current_difficulty': self.current_difficulty,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'ended_at': self.ended_at.isoformat() if self.ended_at else None,
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'events': [event.to_dict() for event in self.events],
            'context_data': self.context_data,
            'concepts_attempted': self.concepts_attempted,
            'concepts_mastered': self.concepts_mastered,
            'questions_answered': self.questions_answered,
            'correct_answers': self.correct_answers,
            'hints_used': self.hints_used,
            'total_time': self.total_time,
            'active_time': self.active_time,
            'idle_time': self.idle_time,
            'average_response_time': self.average_response_time,
            'difficulty_adjustments': self.difficulty_adjustments,
            'learning_path': self.learning_path
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LearningSession':
        """Create LearningSession from dictionary."""
        created_at = datetime.fromisoformat(data['created_at']) if data.get('created_at') else datetime.utcnow()
        started_at = datetime.fromisoformat(data['started_at']) if data.get('started_at') else None
        ended_at = datetime.fromisoformat(data['ended_at']) if data.get('ended_at') else None
        last_activity = datetime.fromisoformat(data['last_activity']) if data.get('last_activity') else None
        
        events = [LearningEvent.from_dict(event_data) for event_data in data.get('events', [])]
        
        return cls(
            session_id=data['session_id'],
            student_id=data['student_id'],
            session_type=data.get('session_type', 'general'),
            learning_objectives=data.get('learning_objectives', []),
            target_concepts=data.get('target_concepts', []),
            state=SessionState(data.get('state', 'created')),
            current_concept=data.get('current_concept'),
            current_difficulty=data.get('current_difficulty', 0.5),
            created_at=created_at,
            started_at=started_at,
            ended_at=ended_at,
            last_activity=last_activity,
            events=events,
            context_data=data.get('context_data', {}),
            concepts_attempted=data.get('concepts_attempted', []),
            concepts_mastered=data.get('concepts_mastered', []),
            questions_answered=data.get('questions_answered', 0),
            correct_answers=data.get('correct_answers', 0),
            hints_used=data.get('hints_used', 0),
            total_time=data.get('total_time', 0),
            active_time=data.get('active_time', 0),
            idle_time=data.get('idle_time', 0),
            average_response_time=data.get('average_response_time', 0.0),
            difficulty_adjustments=data.get('difficulty_adjustments', []),
            learning_path=data.get('learning_path', [])
        )
