"""
Collaboration and community data models for TutorX-MCP.

This module defines data structures for:
- User management (students and educators)
- Study groups and peer learning
- Chat and messaging systems
- Shared resources and content curation
- Collaborative sessions (whiteboards, documents)
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import json


class UserRole(Enum):
    """User roles in the system."""
    STUDENT = "student"
    EDUCATOR = "educator"
    ADMIN = "admin"
    MODERATOR = "moderator"


class MessageType(Enum):
    """Types of chat messages."""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    SYSTEM = "system"
    WHITEBOARD_SHARE = "whiteboard_share"
    DOCUMENT_SHARE = "document_share"


class ResourceType(Enum):
    """Types of shared resources."""
    DOCUMENT = "document"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    PRESENTATION = "presentation"
    WORKSHEET = "worksheet"
    QUIZ = "quiz"
    OTHER = "other"


class SessionType(Enum):
    """Types of collaborative sessions."""
    WHITEBOARD = "whiteboard"
    DOCUMENT = "document"
    SCREEN_SHARE = "screen_share"
    STUDY_SESSION = "study_session"


@dataclass
class User:
    """Extended user model for collaboration features."""
    user_id: str
    username: str
    email: str
    role: UserRole
    display_name: Optional[str] = None
    avatar_url: Optional[str] = None
    bio: Optional[str] = None
    
    # Profile information
    grade_level: Optional[str] = None
    subjects_of_interest: List[str] = field(default_factory=list)
    expertise_areas: List[str] = field(default_factory=list)  # For educators
    
    # Activity tracking
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_active: Optional[datetime] = None
    is_online: bool = False
    
    # Privacy settings
    profile_visibility: str = "public"  # public, friends, private
    allow_messages: bool = True
    allow_study_group_invites: bool = True
    
    # Statistics
    study_groups_joined: int = 0
    resources_shared: int = 0
    messages_sent: int = 0
    reputation_score: int = 0
    
    def update_last_active(self):
        """Update last active timestamp."""
        self.last_active = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'email': self.email,
            'role': self.role.value,
            'display_name': self.display_name,
            'avatar_url': self.avatar_url,
            'bio': self.bio,
            'grade_level': self.grade_level,
            'subjects_of_interest': self.subjects_of_interest,
            'expertise_areas': self.expertise_areas,
            'created_at': self.created_at.isoformat(),
            'last_active': self.last_active.isoformat() if self.last_active else None,
            'is_online': self.is_online,
            'profile_visibility': self.profile_visibility,
            'allow_messages': self.allow_messages,
            'allow_study_group_invites': self.allow_study_group_invites,
            'study_groups_joined': self.study_groups_joined,
            'resources_shared': self.resources_shared,
            'messages_sent': self.messages_sent,
            'reputation_score': self.reputation_score
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """Create User from dictionary."""
        created_at = datetime.fromisoformat(data['created_at']) if data.get('created_at') else datetime.utcnow()
        last_active = datetime.fromisoformat(data['last_active']) if data.get('last_active') else None
        
        return cls(
            user_id=data['user_id'],
            username=data['username'],
            email=data['email'],
            role=UserRole(data['role']),
            display_name=data.get('display_name'),
            avatar_url=data.get('avatar_url'),
            bio=data.get('bio'),
            grade_level=data.get('grade_level'),
            subjects_of_interest=data.get('subjects_of_interest', []),
            expertise_areas=data.get('expertise_areas', []),
            created_at=created_at,
            last_active=last_active,
            is_online=data.get('is_online', False),
            profile_visibility=data.get('profile_visibility', 'public'),
            allow_messages=data.get('allow_messages', True),
            allow_study_group_invites=data.get('allow_study_group_invites', True),
            study_groups_joined=data.get('study_groups_joined', 0),
            resources_shared=data.get('resources_shared', 0),
            messages_sent=data.get('messages_sent', 0),
            reputation_score=data.get('reputation_score', 0)
        )


@dataclass
class StudyGroup:
    """Study group for collaborative learning."""
    group_id: str
    name: str
    description: str
    creator_id: str
    
    # Group settings
    is_public: bool = True
    max_members: int = 20
    subject_focus: Optional[str] = None
    grade_level: Optional[str] = None
    
    # Members and permissions
    members: List[str] = field(default_factory=list)  # user_ids
    moderators: List[str] = field(default_factory=list)  # user_ids
    banned_users: List[str] = field(default_factory=list)  # user_ids
    
    # Activity tracking
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_activity: Optional[datetime] = None
    message_count: int = 0
    
    # Group features
    allow_file_sharing: bool = True
    allow_whiteboard: bool = True
    allow_screen_sharing: bool = True
    
    # Tags and categorization
    tags: List[str] = field(default_factory=list)
    
    def add_member(self, user_id: str) -> bool:
        """Add a member to the group."""
        if user_id not in self.members and len(self.members) < self.max_members:
            self.members.append(user_id)
            self.last_activity = datetime.utcnow()
            return True
        return False
    
    def remove_member(self, user_id: str) -> bool:
        """Remove a member from the group."""
        if user_id in self.members:
            self.members.remove(user_id)
            if user_id in self.moderators:
                self.moderators.remove(user_id)
            self.last_activity = datetime.utcnow()
            return True
        return False
    
    def is_member(self, user_id: str) -> bool:
        """Check if user is a member."""
        return user_id in self.members
    
    def is_moderator(self, user_id: str) -> bool:
        """Check if user is a moderator."""
        return user_id in self.moderators or user_id == self.creator_id
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'group_id': self.group_id,
            'name': self.name,
            'description': self.description,
            'creator_id': self.creator_id,
            'is_public': self.is_public,
            'max_members': self.max_members,
            'subject_focus': self.subject_focus,
            'grade_level': self.grade_level,
            'members': self.members,
            'moderators': self.moderators,
            'banned_users': self.banned_users,
            'created_at': self.created_at.isoformat(),
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'message_count': self.message_count,
            'allow_file_sharing': self.allow_file_sharing,
            'allow_whiteboard': self.allow_whiteboard,
            'allow_screen_sharing': self.allow_screen_sharing,
            'tags': self.tags
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StudyGroup':
        """Create StudyGroup from dictionary."""
        created_at = datetime.fromisoformat(data['created_at']) if data.get('created_at') else datetime.utcnow()
        last_activity = datetime.fromisoformat(data['last_activity']) if data.get('last_activity') else None
        
        return cls(
            group_id=data['group_id'],
            name=data['name'],
            description=data['description'],
            creator_id=data['creator_id'],
            is_public=data.get('is_public', True),
            max_members=data.get('max_members', 20),
            subject_focus=data.get('subject_focus'),
            grade_level=data.get('grade_level'),
            members=data.get('members', []),
            moderators=data.get('moderators', []),
            banned_users=data.get('banned_users', []),
            created_at=created_at,
            last_activity=last_activity,
            message_count=data.get('message_count', 0),
            allow_file_sharing=data.get('allow_file_sharing', True),
            allow_whiteboard=data.get('allow_whiteboard', True),
            allow_screen_sharing=data.get('allow_screen_sharing', True),
            tags=data.get('tags', [])
        )


@dataclass
class ChatMessage:
    """Chat message in study groups or direct messages."""
    message_id: str
    sender_id: str
    group_id: Optional[str] = None  # None for direct messages
    recipient_id: Optional[str] = None  # For direct messages

    # Message content
    message_type: MessageType = MessageType.TEXT
    content: str = ""
    file_url: Optional[str] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = None

    # Threading and replies
    reply_to_message_id: Optional[str] = None
    thread_id: Optional[str] = None

    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    edited_at: Optional[datetime] = None

    # Message status
    is_edited: bool = False
    is_deleted: bool = False
    is_pinned: bool = False

    # Reactions and interactions
    reactions: Dict[str, List[str]] = field(default_factory=dict)  # emoji -> [user_ids]
    mentions: List[str] = field(default_factory=list)  # mentioned user_ids

    def add_reaction(self, emoji: str, user_id: str) -> bool:
        """Add a reaction to the message."""
        if emoji not in self.reactions:
            self.reactions[emoji] = []
        if user_id not in self.reactions[emoji]:
            self.reactions[emoji].append(user_id)
            return True
        return False

    def remove_reaction(self, emoji: str, user_id: str) -> bool:
        """Remove a reaction from the message."""
        if emoji in self.reactions and user_id in self.reactions[emoji]:
            self.reactions[emoji].remove(user_id)
            if not self.reactions[emoji]:
                del self.reactions[emoji]
            return True
        return False

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'message_id': self.message_id,
            'sender_id': self.sender_id,
            'group_id': self.group_id,
            'recipient_id': self.recipient_id,
            'message_type': self.message_type.value,
            'content': self.content,
            'file_url': self.file_url,
            'file_name': self.file_name,
            'file_size': self.file_size,
            'reply_to_message_id': self.reply_to_message_id,
            'thread_id': self.thread_id,
            'created_at': self.created_at.isoformat(),
            'edited_at': self.edited_at.isoformat() if self.edited_at else None,
            'is_edited': self.is_edited,
            'is_deleted': self.is_deleted,
            'is_pinned': self.is_pinned,
            'reactions': self.reactions,
            'mentions': self.mentions
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatMessage':
        """Create ChatMessage from dictionary."""
        created_at = datetime.fromisoformat(data['created_at']) if data.get('created_at') else datetime.utcnow()
        edited_at = datetime.fromisoformat(data['edited_at']) if data.get('edited_at') else None

        return cls(
            message_id=data['message_id'],
            sender_id=data['sender_id'],
            group_id=data.get('group_id'),
            recipient_id=data.get('recipient_id'),
            message_type=MessageType(data.get('message_type', 'text')),
            content=data.get('content', ''),
            file_url=data.get('file_url'),
            file_name=data.get('file_name'),
            file_size=data.get('file_size'),
            reply_to_message_id=data.get('reply_to_message_id'),
            thread_id=data.get('thread_id'),
            created_at=created_at,
            edited_at=edited_at,
            is_edited=data.get('is_edited', False),
            is_deleted=data.get('is_deleted', False),
            is_pinned=data.get('is_pinned', False),
            reactions=data.get('reactions', {}),
            mentions=data.get('mentions', [])
        )


@dataclass
class SharedResource:
    """Shared educational resource."""
    resource_id: str
    title: str
    description: str
    uploader_id: str

    # Resource details
    resource_type: ResourceType
    file_url: str
    file_name: str
    file_size: int
    thumbnail_url: Optional[str] = None

    # Categorization
    subject: Optional[str] = None
    grade_level: Optional[str] = None
    topics: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)

    # Access control
    is_public: bool = True
    shared_with_groups: List[str] = field(default_factory=list)  # group_ids
    shared_with_users: List[str] = field(default_factory=list)  # user_ids

    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)

    # Engagement metrics
    view_count: int = 0
    download_count: int = 0
    like_count: int = 0

    # Reviews and ratings
    ratings: List[Dict[str, Any]] = field(default_factory=list)  # [{'user_id': str, 'rating': int, 'review': str}]
    average_rating: float = 0.0

    # Content curation
    is_featured: bool = False
    is_approved: bool = True
    moderation_notes: Optional[str] = None

    def add_rating(self, user_id: str, rating: int, review: str = "") -> bool:
        """Add or update a rating for the resource."""
        # Remove existing rating from same user
        self.ratings = [r for r in self.ratings if r['user_id'] != user_id]

        # Add new rating
        self.ratings.append({
            'user_id': user_id,
            'rating': rating,
            'review': review,
            'created_at': datetime.utcnow().isoformat()
        })

        # Recalculate average
        if self.ratings:
            self.average_rating = sum(r['rating'] for r in self.ratings) / len(self.ratings)

        self.updated_at = datetime.utcnow()
        return True

    def increment_view_count(self):
        """Increment view count."""
        self.view_count += 1

    def increment_download_count(self):
        """Increment download count."""
        self.download_count += 1

    def increment_like_count(self):
        """Increment like count."""
        self.like_count += 1

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'resource_id': self.resource_id,
            'title': self.title,
            'description': self.description,
            'uploader_id': self.uploader_id,
            'resource_type': self.resource_type.value,
            'file_url': self.file_url,
            'file_name': self.file_name,
            'file_size': self.file_size,
            'thumbnail_url': self.thumbnail_url,
            'subject': self.subject,
            'grade_level': self.grade_level,
            'topics': self.topics,
            'tags': self.tags,
            'is_public': self.is_public,
            'shared_with_groups': self.shared_with_groups,
            'shared_with_users': self.shared_with_users,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'view_count': self.view_count,
            'download_count': self.download_count,
            'like_count': self.like_count,
            'ratings': self.ratings,
            'average_rating': self.average_rating,
            'is_featured': self.is_featured,
            'is_approved': self.is_approved,
            'moderation_notes': self.moderation_notes
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SharedResource':
        """Create SharedResource from dictionary."""
        created_at = datetime.fromisoformat(data['created_at']) if data.get('created_at') else datetime.utcnow()
        updated_at = datetime.fromisoformat(data['updated_at']) if data.get('updated_at') else datetime.utcnow()

        return cls(
            resource_id=data['resource_id'],
            title=data['title'],
            description=data['description'],
            uploader_id=data['uploader_id'],
            resource_type=ResourceType(data['resource_type']),
            file_url=data['file_url'],
            file_name=data['file_name'],
            file_size=data['file_size'],
            thumbnail_url=data.get('thumbnail_url'),
            subject=data.get('subject'),
            grade_level=data.get('grade_level'),
            topics=data.get('topics', []),
            tags=data.get('tags', []),
            is_public=data.get('is_public', True),
            shared_with_groups=data.get('shared_with_groups', []),
            shared_with_users=data.get('shared_with_users', []),
            created_at=created_at,
            updated_at=updated_at,
            view_count=data.get('view_count', 0),
            download_count=data.get('download_count', 0),
            like_count=data.get('like_count', 0),
            ratings=data.get('ratings', []),
            average_rating=data.get('average_rating', 0.0),
            is_featured=data.get('is_featured', False),
            is_approved=data.get('is_approved', True),
            moderation_notes=data.get('moderation_notes')
        )


@dataclass
class CollaborativeSession:
    """Real-time collaborative session (whiteboard, document editing, etc.)."""
    session_id: str
    session_type: SessionType
    title: str
    creator_id: str

    # Session settings
    is_public: bool = False
    max_participants: int = 10
    allow_anonymous: bool = False

    # Participants
    participants: List[str] = field(default_factory=list)  # user_ids
    moderators: List[str] = field(default_factory=list)  # user_ids

    # Session data
    content_data: Dict[str, Any] = field(default_factory=dict)  # Session-specific data
    version: int = 1
    last_modified_by: Optional[str] = None

    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    last_activity: Optional[datetime] = None

    # Session status
    is_active: bool = False
    is_recording: bool = False
    recording_url: Optional[str] = None

    # Associated resources
    shared_resources: List[str] = field(default_factory=list)  # resource_ids
    chat_enabled: bool = True

    def add_participant(self, user_id: str) -> bool:
        """Add a participant to the session."""
        if user_id not in self.participants and len(self.participants) < self.max_participants:
            self.participants.append(user_id)
            self.last_activity = datetime.utcnow()
            return True
        return False

    def remove_participant(self, user_id: str) -> bool:
        """Remove a participant from the session."""
        if user_id in self.participants:
            self.participants.remove(user_id)
            if user_id in self.moderators:
                self.moderators.remove(user_id)
            self.last_activity = datetime.utcnow()
            return True
        return False

    def start_session(self):
        """Start the collaborative session."""
        self.is_active = True
        self.started_at = datetime.utcnow()
        self.last_activity = datetime.utcnow()

    def end_session(self):
        """End the collaborative session."""
        self.is_active = False
        self.ended_at = datetime.utcnow()

    def update_content(self, content_data: Dict[str, Any], modified_by: str):
        """Update session content."""
        self.content_data = content_data
        self.last_modified_by = modified_by
        self.version += 1
        self.last_activity = datetime.utcnow()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'session_id': self.session_id,
            'session_type': self.session_type.value,
            'title': self.title,
            'creator_id': self.creator_id,
            'is_public': self.is_public,
            'max_participants': self.max_participants,
            'allow_anonymous': self.allow_anonymous,
            'participants': self.participants,
            'moderators': self.moderators,
            'content_data': self.content_data,
            'version': self.version,
            'last_modified_by': self.last_modified_by,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'ended_at': self.ended_at.isoformat() if self.ended_at else None,
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'is_active': self.is_active,
            'is_recording': self.is_recording,
            'recording_url': self.recording_url,
            'shared_resources': self.shared_resources,
            'chat_enabled': self.chat_enabled
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CollaborativeSession':
        """Create CollaborativeSession from dictionary."""
        created_at = datetime.fromisoformat(data['created_at']) if data.get('created_at') else datetime.utcnow()
        started_at = datetime.fromisoformat(data['started_at']) if data.get('started_at') else None
        ended_at = datetime.fromisoformat(data['ended_at']) if data.get('ended_at') else None
        last_activity = datetime.fromisoformat(data['last_activity']) if data.get('last_activity') else None

        return cls(
            session_id=data['session_id'],
            session_type=SessionType(data['session_type']),
            title=data['title'],
            creator_id=data['creator_id'],
            is_public=data.get('is_public', False),
            max_participants=data.get('max_participants', 10),
            allow_anonymous=data.get('allow_anonymous', False),
            participants=data.get('participants', []),
            moderators=data.get('moderators', []),
            content_data=data.get('content_data', {}),
            version=data.get('version', 1),
            last_modified_by=data.get('last_modified_by'),
            created_at=created_at,
            started_at=started_at,
            ended_at=ended_at,
            last_activity=last_activity,
            is_active=data.get('is_active', False),
            is_recording=data.get('is_recording', False),
            recording_url=data.get('recording_url'),
            shared_resources=data.get('shared_resources', []),
            chat_enabled=data.get('chat_enabled', True)
        )
