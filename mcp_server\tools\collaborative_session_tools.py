"""
Collaborative session tools for TutorX-MCP.

This module provides MCP tools for real-time collaborative sessions
including shared whiteboards, document editing, and study sessions.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

from ..mcp_instance import mcp
from ..models.collaboration_models import CollaborativeSession, SessionType
from ..storage.memory_store import MemoryStore

# Initialize storage
session_store = MemoryStore(persistence_file="collaborative_sessions_data.json")


@mcp.tool()
async def create_collaborative_session(
    creator_id: str,
    session_type: str,
    title: str,
    is_public: bool = False,
    max_participants: int = 10,
    allow_anonymous: bool = False
) -> Dict[str, Any]:
    """
    Create a new collaborative session.
    
    Args:
        creator_id: ID of the user creating the session
        session_type: Type of session (whiteboard, document, screen_share, study_session)
        title: Session title
        is_public: Whether the session is publicly accessible
        max_participants: Maximum number of participants
        allow_anonymous: Whether to allow anonymous participants
    
    Returns:
        Session creation result
    """
    try:
        # Verify creator exists
        creator = session_store.get_user(creator_id)
        if not creator:
            return {
                "success": False,
                "error": "Creator not found"
            }
        
        # Create collaborative session
        session_id = str(uuid.uuid4())
        session = CollaborativeSession(
            session_id=session_id,
            session_type=SessionType(session_type),
            title=title,
            creator_id=creator_id,
            is_public=is_public,
            max_participants=max_participants,
            allow_anonymous=allow_anonymous,
            participants=[creator_id],  # Creator is automatically a participant
            moderators=[creator_id]  # Creator is automatically a moderator
        )
        
        # Save session
        if session_store.save_collaborative_session(session):
            return {
                "success": True,
                "session": session.to_dict()
            }
        else:
            return {
                "success": False,
                "error": "Failed to save session"
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error creating collaborative session: {str(e)}"
        }


@mcp.tool()
async def join_collaborative_session(
    session_id: str,
    user_id: str
) -> Dict[str, Any]:
    """
    Join a collaborative session.
    
    Args:
        session_id: Session ID
        user_id: User ID
    
    Returns:
        Join result
    """
    try:
        # Get session
        session = session_store.get_collaborative_session(session_id)
        if not session:
            return {
                "success": False,
                "error": "Session not found"
            }
        
        # Check if session is active
        if not session.is_active:
            return {
                "success": False,
                "error": "Session is not active"
            }
        
        # Check if user exists (unless anonymous is allowed)
        if not session.allow_anonymous:
            user = session_store.get_user(user_id)
            if not user:
                return {
                    "success": False,
                    "error": "User not found"
                }
        
        # Add participant
        if session_store.add_session_participant(session_id, user_id):
            return {
                "success": True,
                "message": f"Successfully joined session '{session.title}'",
                "session": session.to_dict()
            }
        else:
            return {
                "success": False,
                "error": "Failed to join session (may be full or user already a participant)"
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error joining session: {str(e)}"
        }


@mcp.tool()
async def leave_collaborative_session(
    session_id: str,
    user_id: str
) -> Dict[str, Any]:
    """
    Leave a collaborative session.
    
    Args:
        session_id: Session ID
        user_id: User ID
    
    Returns:
        Leave result
    """
    try:
        # Remove participant
        if session_store.remove_session_participant(session_id, user_id):
            return {
                "success": True,
                "message": "Successfully left the session"
            }
        else:
            return {
                "success": False,
                "error": "Failed to leave session (user may not be a participant)"
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error leaving session: {str(e)}"
        }


@mcp.tool()
async def start_collaborative_session(
    session_id: str,
    user_id: str
) -> Dict[str, Any]:
    """
    Start a collaborative session.
    
    Args:
        session_id: Session ID
        user_id: ID of the user starting the session (must be creator or moderator)
    
    Returns:
        Start result
    """
    try:
        # Get session
        session = session_store.get_collaborative_session(session_id)
        if not session:
            return {
                "success": False,
                "error": "Session not found"
            }
        
        # Check if user can start the session
        if user_id != session.creator_id and user_id not in session.moderators:
            return {
                "success": False,
                "error": "Only the creator or moderators can start the session"
            }
        
        # Start session
        session.start_session()
        session_store.save_collaborative_session(session)
        
        return {
            "success": True,
            "message": "Session started successfully",
            "session": session.to_dict()
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error starting session: {str(e)}"
        }


@mcp.tool()
async def end_collaborative_session(
    session_id: str,
    user_id: str
) -> Dict[str, Any]:
    """
    End a collaborative session.
    
    Args:
        session_id: Session ID
        user_id: ID of the user ending the session (must be creator or moderator)
    
    Returns:
        End result
    """
    try:
        # Get session
        session = session_store.get_collaborative_session(session_id)
        if not session:
            return {
                "success": False,
                "error": "Session not found"
            }
        
        # Check if user can end the session
        if user_id != session.creator_id and user_id not in session.moderators:
            return {
                "success": False,
                "error": "Only the creator or moderators can end the session"
            }
        
        # End session
        session.end_session()
        session_store.save_collaborative_session(session)
        
        return {
            "success": True,
            "message": "Session ended successfully",
            "session": session.to_dict()
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error ending session: {str(e)}"
        }


@mcp.tool()
async def update_session_content(
    session_id: str,
    user_id: str,
    content_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Update the content of a collaborative session.
    
    Args:
        session_id: Session ID
        user_id: ID of the user updating the content
        content_data: New content data
    
    Returns:
        Update result
    """
    try:
        # Get session
        session = session_store.get_collaborative_session(session_id)
        if not session:
            return {
                "success": False,
                "error": "Session not found"
            }
        
        # Check if user is a participant
        if user_id not in session.participants:
            return {
                "success": False,
                "error": "User is not a participant in this session"
            }
        
        # Update content
        if session_store.update_session_content(session_id, content_data, user_id):
            return {
                "success": True,
                "message": "Content updated successfully",
                "version": session.version
            }
        else:
            return {
                "success": False,
                "error": "Failed to update content"
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error updating session content: {str(e)}"
        }


@mcp.tool()
async def get_session_content(
    session_id: str,
    user_id: str
) -> Dict[str, Any]:
    """
    Get the current content of a collaborative session.
    
    Args:
        session_id: Session ID
        user_id: ID of the user requesting the content
    
    Returns:
        Session content
    """
    try:
        # Get session
        session = session_store.get_collaborative_session(session_id)
        if not session:
            return {
                "success": False,
                "error": "Session not found"
            }
        
        # Check access permissions
        if not session.is_public and user_id not in session.participants:
            return {
                "success": False,
                "error": "Access denied to this session"
            }
        
        return {
            "success": True,
            "content_data": session.content_data,
            "version": session.version,
            "last_modified_by": session.last_modified_by,
            "last_activity": session.last_activity.isoformat() if session.last_activity else None
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error getting session content: {str(e)}"
        }


@mcp.tool()
async def get_user_collaborative_sessions(
    user_id: str,
    active_only: bool = False
) -> Dict[str, Any]:
    """
    Get all collaborative sessions for a user.
    
    Args:
        user_id: User ID
        active_only: Whether to return only active sessions
    
    Returns:
        List of user's collaborative sessions
    """
    try:
        sessions = session_store.get_user_collaborative_sessions(user_id, active_only)
        return {
            "success": True,
            "sessions": [session.to_dict() for session in sessions]
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error getting user sessions: {str(e)}"
        }


@mcp.tool()
async def get_active_collaborative_sessions() -> Dict[str, Any]:
    """
    Get all currently active collaborative sessions.
    
    Returns:
        List of active sessions
    """
    try:
        sessions = session_store.get_active_collaborative_sessions()
        return {
            "success": True,
            "sessions": [session.to_dict() for session in sessions]
        }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"Error getting active sessions: {str(e)}"
        }
