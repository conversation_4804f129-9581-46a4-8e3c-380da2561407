"""
Test script for collaboration and community features.

This script tests the basic functionality of the collaboration features
including user management, study groups, messaging, and content sharing.
"""

import async<PERSON>
import json
from mcp_server.tools.collaboration_tools import (
    create_user, create_study_group, join_study_group, 
    send_group_message, get_group_messages
)
from mcp_server.tools.content_sharing_tools import (
    upload_shared_resource, search_shared_resources, rate_resource
)
from mcp_server.tools.collaborative_session_tools import (
    create_collaborative_session, join_collaborative_session,
    start_collaborative_session, update_session_content
)


async def test_collaboration_features():
    """Test the collaboration features."""
    print("🚀 Testing TutorX Collaboration Features")
    print("=" * 50)
    
    # Test 1: Create Users
    print("\n1. Creating Users...")
    
    # Create a student
    student_result = await create_user(
        username="alice_student",
        email="<EMAIL>",
        role="student",
        display_name="<PERSON>",
        grade_level="10th Grade",
        subjects_of_interest=["Mathematics", "Physics"]
    )
    print(f"Student creation: {student_result}")
    
    # Create an educator
    educator_result = await create_user(
        username="bob_teacher",
        email="<EMAIL>",
        role="educator",
        display_name="<PERSON>",
        subjects_of_interest=["Mathematics", "Computer Science"]
    )
    print(f"Educator creation: {educator_result}")
    
    if not (student_result.get("success") and educator_result.get("success")):
        print("❌ User creation failed!")
        return
    
    student_id = student_result["user"]["user_id"]
    educator_id = educator_result["user"]["user_id"]
    
    # Test 2: Create Study Group
    print("\n2. Creating Study Group...")
    
    group_result = await create_study_group(
        creator_id=educator_id,
        name="Advanced Mathematics Study Group",
        description="A group for students studying advanced mathematics topics",
        subject_focus="Mathematics",
        grade_level="10th Grade",
        is_public=True,
        max_members=20
    )
    print(f"Study group creation: {group_result}")
    
    if not group_result.get("success"):
        print("❌ Study group creation failed!")
        return
    
    group_id = group_result["study_group"]["group_id"]
    
    # Test 3: Join Study Group
    print("\n3. Joining Study Group...")
    
    join_result = await join_study_group(group_id, student_id)
    print(f"Join group result: {join_result}")
    
    # Test 4: Send Group Message
    print("\n4. Sending Group Message...")
    
    message_result = await send_group_message(
        sender_id=student_id,
        group_id=group_id,
        content="Hello everyone! Excited to be part of this study group!",
        message_type="text"
    )
    print(f"Message sent: {message_result}")
    
    # Test 5: Get Group Messages
    print("\n5. Getting Group Messages...")
    
    messages_result = await get_group_messages(
        group_id=group_id,
        user_id=student_id,
        limit=10
    )
    print(f"Group messages: {messages_result}")
    
    # Test 6: Upload Shared Resource
    print("\n6. Uploading Shared Resource...")
    
    resource_result = await upload_shared_resource(
        uploader_id=educator_id,
        title="Quadratic Equations Worksheet",
        description="Practice problems for quadratic equations with step-by-step solutions",
        resource_type="worksheet",
        file_url="https://example.com/quadratic_worksheet.pdf",
        file_name="quadratic_worksheet.pdf",
        file_size=1024000,
        subject="Mathematics",
        grade_level="10th Grade",
        topics=["Algebra", "Quadratic Equations"],
        tags=["practice", "homework", "algebra"],
        is_public=True
    )
    print(f"Resource upload: {resource_result}")
    
    if resource_result.get("success"):
        resource_id = resource_result["resource"]["resource_id"]
        
        # Test 7: Rate Resource
        print("\n7. Rating Resource...")
        
        rating_result = await rate_resource(
            resource_id=resource_id,
            user_id=student_id,
            rating=5,
            review="Excellent worksheet! Very helpful for understanding quadratic equations."
        )
        print(f"Resource rating: {rating_result}")
    
    # Test 8: Search Resources
    print("\n8. Searching Resources...")
    
    search_result = await search_shared_resources(
        query="quadratic",
        resource_type="worksheet",
        subject="Mathematics",
        grade_level="10th Grade",
        limit=10
    )
    print(f"Resource search: {search_result}")
    
    # Test 9: Create Collaborative Session
    print("\n9. Creating Collaborative Session...")
    
    session_result = await create_collaborative_session(
        creator_id=educator_id,
        session_type="whiteboard",
        title="Quadratic Equations Problem Solving",
        is_public=False,
        max_participants=5
    )
    print(f"Session creation: {session_result}")
    
    if session_result.get("success"):
        session_id = session_result["session"]["session_id"]
        
        # Test 10: Join Collaborative Session
        print("\n10. Joining Collaborative Session...")
        
        join_session_result = await join_collaborative_session(session_id, student_id)
        print(f"Join session: {join_session_result}")
        
        # Test 11: Start Session
        print("\n11. Starting Collaborative Session...")
        
        start_session_result = await start_collaborative_session(session_id, educator_id)
        print(f"Start session: {start_session_result}")
        
        # Test 12: Update Session Content
        print("\n12. Updating Session Content...")
        
        content_update_result = await update_session_content(
            session_id=session_id,
            user_id=educator_id,
            content_data={
                "whiteboard_data": {
                    "shapes": [
                        {"type": "text", "content": "Solve: x² + 5x + 6 = 0", "x": 100, "y": 50},
                        {"type": "line", "x1": 50, "y1": 100, "x2": 300, "y2": 100}
                    ],
                    "background_color": "#ffffff"
                }
            }
        )
        print(f"Content update: {content_update_result}")
    
    print("\n" + "=" * 50)
    print("✅ Collaboration features test completed!")
    print("\nFeatures tested:")
    print("- ✅ User creation (student and educator)")
    print("- ✅ Study group creation and joining")
    print("- ✅ Group messaging")
    print("- ✅ Content sharing and rating")
    print("- ✅ Resource search")
    print("- ✅ Collaborative sessions (whiteboard)")
    print("- ✅ Real-time content updates")


async def test_error_handling():
    """Test error handling scenarios."""
    print("\n🔍 Testing Error Handling...")
    print("-" * 30)
    
    # Test invalid user creation
    invalid_user = await create_user("", "", "invalid_role")
    print(f"Invalid user creation: {invalid_user}")
    
    # Test joining non-existent group
    invalid_join = await join_study_group("non_existent_group", "non_existent_user")
    print(f"Invalid group join: {invalid_join}")
    
    # Test uploading resource with missing data
    invalid_resource = await upload_shared_resource(
        uploader_id="",
        title="",
        description="",
        resource_type="invalid_type",
        file_url="",
        file_name="",
        file_size=0
    )
    print(f"Invalid resource upload: {invalid_resource}")
    
    print("✅ Error handling tests completed!")


if __name__ == "__main__":
    print("Starting TutorX Collaboration Features Test...")
    
    try:
        # Run the main test
        asyncio.run(test_collaboration_features())
        
        # Run error handling tests
        asyncio.run(test_error_handling())
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎉 All tests completed!")
