"""
Session manager for TutorX-MCP collaboration features.

This module provides session management for collaborative sessions,
user sessions, and real-time collaboration state.
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from collections import defaultdict
import threading
import asyncio

from ..models.collaboration_models import CollaborativeSession, User


class SessionManager:
    """
    Manages collaborative sessions and user presence.
    
    This provides session lifecycle management, participant tracking,
    and real-time collaboration state management.
    """
    
    def __init__(self):
        """Initialize the session manager."""
        self._lock = threading.RLock()
        
        # Active sessions
        self.active_sessions: Dict[str, CollaborativeSession] = {}
        
        # User presence tracking
        self.online_users: Set[str] = set()
        self.user_sessions: Dict[str, Set[str]] = defaultdict(set)  # user_id -> session_ids
        self.session_participants: Dict[str, Set[str]] = defaultdict(set)  # session_id -> user_ids
        
        # Session activity tracking
        self.session_activity: Dict[str, datetime] = {}
        self.user_activity: Dict[str, datetime] = {}
        
        # Real-time updates queue
        self.update_queue: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        # Cleanup task
        self._cleanup_task = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Start the background cleanup task."""
        try:
            loop = asyncio.get_event_loop()
            self._cleanup_task = loop.create_task(self._periodic_cleanup())
        except RuntimeError:
            # No event loop running, cleanup will be manual
            pass
    
    async def _periodic_cleanup(self):
        """Periodically clean up inactive sessions and users."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                self.cleanup_inactive_sessions()
                self.cleanup_inactive_users()
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Error in periodic cleanup: {e}")
    
    def create_session(self, session: CollaborativeSession) -> bool:
        """
        Create and register a new collaborative session.
        
        Args:
            session: The collaborative session to create
            
        Returns:
            True if session was created successfully
        """
        with self._lock:
            try:
                self.active_sessions[session.session_id] = session
                self.session_activity[session.session_id] = datetime.utcnow()
                
                # Add creator as participant
                self.add_participant(session.session_id, session.creator_id)
                
                return True
            except Exception as e:
                print(f"Error creating session: {e}")
                return False
    
    def get_session(self, session_id: str) -> Optional[CollaborativeSession]:
        """Get a session by ID."""
        with self._lock:
            return self.active_sessions.get(session_id)
    
    def add_participant(self, session_id: str, user_id: str) -> bool:
        """
        Add a participant to a session.
        
        Args:
            session_id: Session ID
            user_id: User ID
            
        Returns:
            True if participant was added successfully
        """
        with self._lock:
            try:
                if session_id not in self.active_sessions:
                    return False
                
                session = self.active_sessions[session_id]
                
                # Check if session is full
                if len(session.participants) >= session.max_participants:
                    return False
                
                # Add participant
                if user_id not in session.participants:
                    session.participants.append(user_id)
                
                # Update tracking
                self.user_sessions[user_id].add(session_id)
                self.session_participants[session_id].add(user_id)
                self.session_activity[session_id] = datetime.utcnow()
                self.user_activity[user_id] = datetime.utcnow()
                
                # Add to online users
                self.online_users.add(user_id)
                
                return True
            except Exception as e:
                print(f"Error adding participant: {e}")
                return False
    
    def remove_participant(self, session_id: str, user_id: str) -> bool:
        """
        Remove a participant from a session.
        
        Args:
            session_id: Session ID
            user_id: User ID
            
        Returns:
            True if participant was removed successfully
        """
        with self._lock:
            try:
                if session_id not in self.active_sessions:
                    return False
                
                session = self.active_sessions[session_id]
                
                # Remove participant
                if user_id in session.participants:
                    session.participants.remove(user_id)
                
                # Update tracking
                self.user_sessions[user_id].discard(session_id)
                self.session_participants[session_id].discard(user_id)
                self.session_activity[session_id] = datetime.utcnow()
                
                # Remove from online users if not in any other sessions
                if not self.user_sessions[user_id]:
                    self.online_users.discard(user_id)
                
                return True
            except Exception as e:
                print(f"Error removing participant: {e}")
                return False
    
    def update_session_activity(self, session_id: str, user_id: str = None):
        """Update session activity timestamp."""
        with self._lock:
            self.session_activity[session_id] = datetime.utcnow()
            if user_id:
                self.user_activity[user_id] = datetime.utcnow()
    
    def get_user_sessions(self, user_id: str) -> List[CollaborativeSession]:
        """Get all active sessions for a user."""
        with self._lock:
            session_ids = self.user_sessions.get(user_id, set())
            return [self.active_sessions[sid] for sid in session_ids 
                   if sid in self.active_sessions]
    
    def get_session_participants(self, session_id: str) -> List[str]:
        """Get all participants in a session."""
        with self._lock:
            return list(self.session_participants.get(session_id, set()))
    
    def is_user_online(self, user_id: str) -> bool:
        """Check if a user is currently online."""
        with self._lock:
            return user_id in self.online_users
    
    def get_online_users(self) -> List[str]:
        """Get all currently online users."""
        with self._lock:
            return list(self.online_users)
    
    def end_session(self, session_id: str) -> bool:
        """
        End a collaborative session.
        
        Args:
            session_id: Session ID
            
        Returns:
            True if session was ended successfully
        """
        with self._lock:
            try:
                if session_id not in self.active_sessions:
                    return False
                
                session = self.active_sessions[session_id]
                session.end_session()
                
                # Remove all participants
                for user_id in list(session.participants):
                    self.remove_participant(session_id, user_id)
                
                # Remove session from active sessions
                del self.active_sessions[session_id]
                
                # Clean up tracking
                if session_id in self.session_activity:
                    del self.session_activity[session_id]
                if session_id in self.session_participants:
                    del self.session_participants[session_id]
                if session_id in self.update_queue:
                    del self.update_queue[session_id]
                
                return True
            except Exception as e:
                print(f"Error ending session: {e}")
                return False
    
    def cleanup_inactive_sessions(self, timeout_minutes: int = 60):
        """Clean up sessions that have been inactive for too long."""
        with self._lock:
            try:
                cutoff_time = datetime.utcnow() - timedelta(minutes=timeout_minutes)
                sessions_to_remove = []
                
                for session_id, last_activity in self.session_activity.items():
                    if last_activity < cutoff_time:
                        sessions_to_remove.append(session_id)
                
                for session_id in sessions_to_remove:
                    self.end_session(session_id)
                
                return len(sessions_to_remove)
            except Exception as e:
                print(f"Error cleaning up inactive sessions: {e}")
                return 0
    
    def cleanup_inactive_users(self, timeout_minutes: int = 30):
        """Clean up users that have been inactive for too long."""
        with self._lock:
            try:
                cutoff_time = datetime.utcnow() - timedelta(minutes=timeout_minutes)
                users_to_remove = []
                
                for user_id, last_activity in self.user_activity.items():
                    if last_activity < cutoff_time:
                        users_to_remove.append(user_id)
                
                for user_id in users_to_remove:
                    # Remove user from all sessions
                    session_ids = list(self.user_sessions.get(user_id, set()))
                    for session_id in session_ids:
                        self.remove_participant(session_id, user_id)
                    
                    # Clean up user tracking
                    self.online_users.discard(user_id)
                    if user_id in self.user_activity:
                        del self.user_activity[user_id]
                    if user_id in self.user_sessions:
                        del self.user_sessions[user_id]
                
                return len(users_to_remove)
            except Exception as e:
                print(f"Error cleaning up inactive users: {e}")
                return 0
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get session manager statistics."""
        with self._lock:
            return {
                'active_sessions': len(self.active_sessions),
                'online_users': len(self.online_users),
                'total_participants': sum(len(participants) for participants in self.session_participants.values()),
                'last_updated': datetime.utcnow().isoformat()
            }
    
    def shutdown(self):
        """Shutdown the session manager."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        # End all active sessions
        with self._lock:
            session_ids = list(self.active_sessions.keys())
            for session_id in session_ids:
                self.end_session(session_id)


# Global session manager instance
session_manager = SessionManager()
