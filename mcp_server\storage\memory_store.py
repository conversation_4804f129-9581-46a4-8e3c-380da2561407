"""
In-memory storage implementation for TutorX-MCP.

This module provides in-memory storage for development and testing.
In production, this would be replaced with database-backed storage.
"""

import json
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import threading
from collections import defaultdict

from ..models.student_profile import StudentProfile
from ..models.collaboration_models import (
    User, StudyGroup, ChatMessage, SharedResource, CollaborativeSession
)


class MemoryStore:
    """
    In-memory storage implementation for adaptive learning data.
    
    This provides a simple storage layer for development and testing.
    In production, this would be replaced with a proper database.
    """
    
    def __init__(self, persistence_file: Optional[str] = None):
        """
        Initialize the memory store.
        
        Args:
            persistence_file: Optional file path for data persistence
        """
        self.persistence_file = persistence_file
        self._lock = threading.RLock()
        
        # Storage containers
        self.student_profiles: Dict[str, StudentProfile] = {}
        self.performance_data: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.session_data: Dict[str, Dict[str, Any]] = {}
        self.analytics_cache: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.adaptation_history: Dict[str, List[Dict[str, Any]]] = defaultdict(list)

        # Collaboration storage containers
        self.users: Dict[str, User] = {}
        self.study_groups: Dict[str, StudyGroup] = {}
        self.chat_messages: Dict[str, List[ChatMessage]] = defaultdict(list)  # group_id -> messages
        self.direct_messages: Dict[str, List[ChatMessage]] = defaultdict(list)  # conversation_id -> messages
        self.shared_resources: Dict[str, SharedResource] = {}
        self.collaborative_sessions: Dict[str, CollaborativeSession] = {}

        # Indexes for efficient querying
        self.user_groups: Dict[str, List[str]] = defaultdict(list)  # user_id -> group_ids
        self.group_members: Dict[str, List[str]] = defaultdict(list)  # group_id -> user_ids
        self.user_resources: Dict[str, List[str]] = defaultdict(list)  # user_id -> resource_ids
        
        # Load persisted data if available
        if self.persistence_file:
            self._load_from_file()
    
    def _load_from_file(self):
        """Load data from persistence file."""
        try:
            if Path(self.persistence_file).exists():
                with open(self.persistence_file, 'rb') as f:
                    data = pickle.load(f)
                    
                self.student_profiles = data.get('student_profiles', {})
                self.performance_data = data.get('performance_data', defaultdict(dict))
                self.session_data = data.get('session_data', {})
                self.analytics_cache = data.get('analytics_cache', defaultdict(dict))
                self.adaptation_history = data.get('adaptation_history', defaultdict(list))
                
                print(f"Loaded data from {self.persistence_file}")
        except Exception as e:
            print(f"Error loading data from {self.persistence_file}: {e}")
    
    def _save_to_file(self):
        """Save data to persistence file."""
        if not self.persistence_file:
            return
        
        try:
            data = {
                'student_profiles': self.student_profiles,
                'performance_data': dict(self.performance_data),
                'session_data': self.session_data,
                'analytics_cache': dict(self.analytics_cache),
                'adaptation_history': dict(self.adaptation_history)
            }
            
            with open(self.persistence_file, 'wb') as f:
                pickle.dump(data, f)
                
        except Exception as e:
            print(f"Error saving data to {self.persistence_file}: {e}")
    
    # Student Profile Operations
    def save_student_profile(self, profile: StudentProfile) -> bool:
        """Save a student profile."""
        with self._lock:
            try:
                self.student_profiles[profile.student_id] = profile
                self._save_to_file()
                return True
            except Exception as e:
                print(f"Error saving student profile: {e}")
                return False
    
    def get_student_profile(self, student_id: str) -> Optional[StudentProfile]:
        """Get a student profile by ID."""
        with self._lock:
            return self.student_profiles.get(student_id)
    
    def update_student_profile(self, student_id: str, updates: Dict[str, Any]) -> bool:
        """Update a student profile with new data."""
        with self._lock:
            try:
                if student_id not in self.student_profiles:
                    return False
                
                profile = self.student_profiles[student_id]
                
                # Update profile attributes
                for key, value in updates.items():
                    if hasattr(profile, key):
                        setattr(profile, key, value)
                
                profile.last_updated = datetime.utcnow()
                self._save_to_file()
                return True
            except Exception as e:
                print(f"Error updating student profile: {e}")
                return False
    
    def delete_student_profile(self, student_id: str) -> bool:
        """Delete a student profile."""
        with self._lock:
            try:
                if student_id in self.student_profiles:
                    del self.student_profiles[student_id]
                    self._save_to_file()
                    return True
                return False
            except Exception as e:
                print(f"Error deleting student profile: {e}")
                return False
    
    def list_student_profiles(self, active_only: bool = False, 
                            days: int = 30) -> List[StudentProfile]:
        """List student profiles, optionally filtering by activity."""
        with self._lock:
            profiles = list(self.student_profiles.values())
            
            if active_only:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                profiles = [
                    p for p in profiles 
                    if p.last_active and p.last_active >= cutoff_date
                ]
            
            return profiles
    
    # Performance Data Operations
    def save_performance_data(self, student_id: str, concept_id: str, 
                            data: Dict[str, Any]) -> bool:
        """Save performance data for a student and concept."""
        with self._lock:
            try:
                if student_id not in self.performance_data:
                    self.performance_data[student_id] = {}
                
                self.performance_data[student_id][concept_id] = {
                    **data,
                    'last_updated': datetime.utcnow().isoformat()
                }
                self._save_to_file()
                return True
            except Exception as e:
                print(f"Error saving performance data: {e}")
                return False
    
    def get_performance_data(self, student_id: str, 
                           concept_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get performance data for a student and optionally a specific concept."""
        with self._lock:
            if student_id not in self.performance_data:
                return None
            
            if concept_id:
                return self.performance_data[student_id].get(concept_id)
            else:
                return self.performance_data[student_id]
    
    def update_performance_data(self, student_id: str, concept_id: str,
                              updates: Dict[str, Any]) -> bool:
        """Update performance data for a student and concept."""
        with self._lock:
            try:
                if student_id not in self.performance_data:
                    self.performance_data[student_id] = {}
                
                if concept_id not in self.performance_data[student_id]:
                    self.performance_data[student_id][concept_id] = {}
                
                self.performance_data[student_id][concept_id].update(updates)
                self.performance_data[student_id][concept_id]['last_updated'] = datetime.utcnow().isoformat()
                self._save_to_file()
                return True
            except Exception as e:
                print(f"Error updating performance data: {e}")
                return False
    
    # Session Data Operations
    def save_session_data(self, session_id: str, data: Dict[str, Any]) -> bool:
        """Save session data."""
        with self._lock:
            try:
                self.session_data[session_id] = {
                    **data,
                    'saved_at': datetime.utcnow().isoformat()
                }
                self._save_to_file()
                return True
            except Exception as e:
                print(f"Error saving session data: {e}")
                return False
    
    def get_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data by ID."""
        with self._lock:
            return self.session_data.get(session_id)
    
    def delete_session_data(self, session_id: str) -> bool:
        """Delete session data."""
        with self._lock:
            try:
                if session_id in self.session_data:
                    del self.session_data[session_id]
                    self._save_to_file()
                    return True
                return False
            except Exception as e:
                print(f"Error deleting session data: {e}")
                return False
    
    def cleanup_old_sessions(self, days: int = 7) -> int:
        """Clean up old session data."""
        with self._lock:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                sessions_to_delete = []
                
                for session_id, data in self.session_data.items():
                    saved_at_str = data.get('saved_at')
                    if saved_at_str:
                        saved_at = datetime.fromisoformat(saved_at_str)
                        if saved_at < cutoff_date:
                            sessions_to_delete.append(session_id)
                
                for session_id in sessions_to_delete:
                    del self.session_data[session_id]
                
                if sessions_to_delete:
                    self._save_to_file()
                
                return len(sessions_to_delete)
            except Exception as e:
                print(f"Error cleaning up old sessions: {e}")
                return 0
    
    # Analytics Cache Operations
    def cache_analytics_result(self, cache_key: str, data: Dict[str, Any],
                             ttl_minutes: int = 60) -> bool:
        """Cache analytics result with TTL."""
        with self._lock:
            try:
                expiry_time = datetime.utcnow() + timedelta(minutes=ttl_minutes)
                self.analytics_cache[cache_key] = {
                    'data': data,
                    'expires_at': expiry_time.isoformat(),
                    'cached_at': datetime.utcnow().isoformat()
                }
                return True
            except Exception as e:
                print(f"Error caching analytics result: {e}")
                return False
    
    def get_cached_analytics(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached analytics result if not expired."""
        with self._lock:
            if cache_key not in self.analytics_cache:
                return None
            
            cached_item = self.analytics_cache[cache_key]
            expires_at = datetime.fromisoformat(cached_item['expires_at'])
            
            if datetime.utcnow() > expires_at:
                # Cache expired, remove it
                del self.analytics_cache[cache_key]
                return None
            
            return cached_item['data']
    
    def clear_analytics_cache(self, pattern: Optional[str] = None) -> int:
        """Clear analytics cache, optionally matching a pattern."""
        with self._lock:
            try:
                if pattern is None:
                    count = len(self.analytics_cache)
                    self.analytics_cache.clear()
                    return count
                else:
                    keys_to_delete = [
                        key for key in self.analytics_cache.keys()
                        if pattern in key
                    ]
                    for key in keys_to_delete:
                        del self.analytics_cache[key]
                    return len(keys_to_delete)
            except Exception as e:
                print(f"Error clearing analytics cache: {e}")
                return 0
    
    # Adaptation History Operations
    def add_adaptation_record(self, student_id: str, record: Dict[str, Any]) -> bool:
        """Add an adaptation record for a student."""
        with self._lock:
            try:
                self.adaptation_history[student_id].append({
                    **record,
                    'recorded_at': datetime.utcnow().isoformat()
                })
                
                # Keep only last 100 records per student
                if len(self.adaptation_history[student_id]) > 100:
                    self.adaptation_history[student_id] = self.adaptation_history[student_id][-100:]
                
                self._save_to_file()
                return True
            except Exception as e:
                print(f"Error adding adaptation record: {e}")
                return False
    
    def get_adaptation_history(self, student_id: str, 
                             days: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get adaptation history for a student."""
        with self._lock:
            if student_id not in self.adaptation_history:
                return []
            
            records = self.adaptation_history[student_id]
            
            if days is not None:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                records = [
                    record for record in records
                    if datetime.fromisoformat(record['recorded_at']) >= cutoff_date
                ]
            
            return records
    
    # Utility Operations
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        with self._lock:
            return {
                'student_profiles_count': len(self.student_profiles),
                'performance_data_students': len(self.performance_data),
                'total_performance_records': sum(
                    len(concepts) for concepts in self.performance_data.values()
                ),
                'active_sessions': len(self.session_data),
                'cached_analytics': len(self.analytics_cache),
                'adaptation_records': sum(
                    len(records) for records in self.adaptation_history.values()
                ),
                'persistence_enabled': self.persistence_file is not None,
                'last_updated': datetime.utcnow().isoformat()
            }
    
    def export_data(self, format: str = 'json') -> Union[str, bytes]:
        """Export all data in specified format."""
        with self._lock:
            data = {
                'student_profiles': {
                    sid: profile.to_dict() 
                    for sid, profile in self.student_profiles.items()
                },
                'performance_data': dict(self.performance_data),
                'session_data': self.session_data,
                'analytics_cache': dict(self.analytics_cache),
                'adaptation_history': dict(self.adaptation_history),
                'exported_at': datetime.utcnow().isoformat()
            }
            
            if format.lower() == 'json':
                return json.dumps(data, indent=2)
            elif format.lower() == 'pickle':
                return pickle.dumps(data)
            else:
                raise ValueError(f"Unsupported export format: {format}")
    
    def import_data(self, data: Union[str, bytes], format: str = 'json') -> bool:
        """Import data from specified format."""
        with self._lock:
            try:
                if format.lower() == 'json':
                    imported_data = json.loads(data)
                elif format.lower() == 'pickle':
                    imported_data = pickle.loads(data)
                else:
                    raise ValueError(f"Unsupported import format: {format}")
                
                # Import student profiles
                if 'student_profiles' in imported_data:
                    for sid, profile_data in imported_data['student_profiles'].items():
                        profile = StudentProfile.from_dict(profile_data)
                        self.student_profiles[sid] = profile
                
                # Import other data
                if 'performance_data' in imported_data:
                    self.performance_data.update(imported_data['performance_data'])
                
                if 'session_data' in imported_data:
                    self.session_data.update(imported_data['session_data'])
                
                if 'analytics_cache' in imported_data:
                    self.analytics_cache.update(imported_data['analytics_cache'])
                
                if 'adaptation_history' in imported_data:
                    self.adaptation_history.update(imported_data['adaptation_history'])
                
                self._save_to_file()
                return True
            except Exception as e:
                print(f"Error importing data: {e}")
                return False

    # Collaboration Operations - User Management
    def save_user(self, user: User) -> bool:
        """Save or update a user."""
        with self._lock:
            try:
                self.users[user.user_id] = user
                self._save_to_file()
                return True
            except Exception as e:
                print(f"Error saving user: {e}")
                return False

    def get_user(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        with self._lock:
            return self.users.get(user_id)

    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        with self._lock:
            for user in self.users.values():
                if user.username == username:
                    return user
            return None

    def get_users_by_role(self, role: str) -> List[User]:
        """Get all users with a specific role."""
        with self._lock:
            return [user for user in self.users.values() if user.role.value == role]

    def search_users(self, query: str, limit: int = 20) -> List[User]:
        """Search users by username or display name."""
        with self._lock:
            query_lower = query.lower()
            results = []
            for user in self.users.values():
                if (query_lower in user.username.lower() or
                    (user.display_name and query_lower in user.display_name.lower())):
                    results.append(user)
                    if len(results) >= limit:
                        break
            return results

    def update_user_activity(self, user_id: str, is_online: bool = True) -> bool:
        """Update user's online status and last activity."""
        with self._lock:
            try:
                if user_id in self.users:
                    self.users[user_id].is_online = is_online
                    self.users[user_id].update_last_active()
                    self._save_to_file()
                    return True
                return False
            except Exception as e:
                print(f"Error updating user activity: {e}")
                return False

    # Study Group Operations
    def save_study_group(self, group: StudyGroup) -> bool:
        """Save or update a study group."""
        with self._lock:
            try:
                self.study_groups[group.group_id] = group

                # Update indexes
                self.group_members[group.group_id] = group.members.copy()
                for member_id in group.members:
                    if group.group_id not in self.user_groups[member_id]:
                        self.user_groups[member_id].append(group.group_id)

                self._save_to_file()
                return True
            except Exception as e:
                print(f"Error saving study group: {e}")
                return False

    def get_study_group(self, group_id: str) -> Optional[StudyGroup]:
        """Get study group by ID."""
        with self._lock:
            return self.study_groups.get(group_id)

    def get_user_study_groups(self, user_id: str) -> List[StudyGroup]:
        """Get all study groups for a user."""
        with self._lock:
            group_ids = self.user_groups.get(user_id, [])
            return [self.study_groups[gid] for gid in group_ids if gid in self.study_groups]

    def search_study_groups(self, query: str = None, subject: str = None,
                           grade_level: str = None, is_public: bool = None,
                           limit: int = 20) -> List[StudyGroup]:
        """Search study groups with filters."""
        with self._lock:
            results = []
            for group in self.study_groups.values():
                # Apply filters
                if is_public is not None and group.is_public != is_public:
                    continue
                if subject and group.subject_focus != subject:
                    continue
                if grade_level and group.grade_level != grade_level:
                    continue
                if query:
                    query_lower = query.lower()
                    if not (query_lower in group.name.lower() or
                           query_lower in group.description.lower()):
                        continue

                results.append(group)
                if len(results) >= limit:
                    break

            return results

    def add_user_to_group(self, group_id: str, user_id: str) -> bool:
        """Add a user to a study group."""
        with self._lock:
            try:
                if group_id in self.study_groups:
                    group = self.study_groups[group_id]
                    if group.add_member(user_id):
                        # Update indexes
                        self.group_members[group_id] = group.members.copy()
                        if group_id not in self.user_groups[user_id]:
                            self.user_groups[user_id].append(group_id)

                        # Update user stats
                        if user_id in self.users:
                            self.users[user_id].study_groups_joined += 1

                        self._save_to_file()
                        return True
                return False
            except Exception as e:
                print(f"Error adding user to group: {e}")
                return False

    def remove_user_from_group(self, group_id: str, user_id: str) -> bool:
        """Remove a user from a study group."""
        with self._lock:
            try:
                if group_id in self.study_groups:
                    group = self.study_groups[group_id]
                    if group.remove_member(user_id):
                        # Update indexes
                        self.group_members[group_id] = group.members.copy()
                        if group_id in self.user_groups[user_id]:
                            self.user_groups[user_id].remove(group_id)

                        # Update user stats
                        if user_id in self.users:
                            self.users[user_id].study_groups_joined = max(0,
                                self.users[user_id].study_groups_joined - 1)

                        self._save_to_file()
                        return True
                return False
            except Exception as e:
                print(f"Error removing user from group: {e}")
                return False

    # Chat Message Operations
    def save_chat_message(self, message: ChatMessage) -> bool:
        """Save a chat message."""
        with self._lock:
            try:
                if message.group_id:
                    # Group message
                    self.chat_messages[message.group_id].append(message)

                    # Update group message count
                    if message.group_id in self.study_groups:
                        self.study_groups[message.group_id].message_count += 1
                        self.study_groups[message.group_id].last_activity = datetime.utcnow()
                else:
                    # Direct message
                    conversation_id = self._get_conversation_id(message.sender_id, message.recipient_id)
                    self.direct_messages[conversation_id].append(message)

                # Update sender stats
                if message.sender_id in self.users:
                    self.users[message.sender_id].messages_sent += 1

                self._save_to_file()
                return True
            except Exception as e:
                print(f"Error saving chat message: {e}")
                return False

    def get_group_messages(self, group_id: str, limit: int = 50,
                          before_message_id: str = None) -> List[ChatMessage]:
        """Get messages for a group."""
        with self._lock:
            messages = self.chat_messages.get(group_id, [])

            if before_message_id:
                # Find the index of the before_message_id
                before_index = None
                for i, msg in enumerate(messages):
                    if msg.message_id == before_message_id:
                        before_index = i
                        break

                if before_index is not None:
                    messages = messages[:before_index]

            # Return the last 'limit' messages
            return messages[-limit:] if len(messages) > limit else messages

    def get_direct_messages(self, user1_id: str, user2_id: str,
                           limit: int = 50) -> List[ChatMessage]:
        """Get direct messages between two users."""
        with self._lock:
            conversation_id = self._get_conversation_id(user1_id, user2_id)
            messages = self.direct_messages.get(conversation_id, [])
            return messages[-limit:] if len(messages) > limit else messages

    def _get_conversation_id(self, user1_id: str, user2_id: str) -> str:
        """Generate a consistent conversation ID for two users."""
        return f"dm_{min(user1_id, user2_id)}_{max(user1_id, user2_id)}"

    def search_messages(self, query: str, group_id: str = None,
                       user_id: str = None, limit: int = 20) -> List[ChatMessage]:
        """Search messages by content."""
        with self._lock:
            results = []
            query_lower = query.lower()

            # Search in group messages
            if group_id:
                messages = self.chat_messages.get(group_id, [])
            else:
                # Search all messages
                messages = []
                for group_messages in self.chat_messages.values():
                    messages.extend(group_messages)
                for dm_messages in self.direct_messages.values():
                    messages.extend(dm_messages)

            for message in messages:
                if (query_lower in message.content.lower() and
                    (user_id is None or message.sender_id == user_id)):
                    results.append(message)
                    if len(results) >= limit:
                        break

            return results

    # Shared Resource Operations
    def save_shared_resource(self, resource: SharedResource) -> bool:
        """Save a shared resource."""
        with self._lock:
            try:
                self.shared_resources[resource.resource_id] = resource

                # Update user resources index
                if resource.resource_id not in self.user_resources[resource.uploader_id]:
                    self.user_resources[resource.uploader_id].append(resource.resource_id)

                # Update user stats
                if resource.uploader_id in self.users:
                    self.users[resource.uploader_id].resources_shared += 1

                self._save_to_file()
                return True
            except Exception as e:
                print(f"Error saving shared resource: {e}")
                return False

    def get_shared_resource(self, resource_id: str) -> Optional[SharedResource]:
        """Get shared resource by ID."""
        with self._lock:
            return self.shared_resources.get(resource_id)

    def get_user_resources(self, user_id: str) -> List[SharedResource]:
        """Get all resources uploaded by a user."""
        with self._lock:
            resource_ids = self.user_resources.get(user_id, [])
            return [self.shared_resources[rid] for rid in resource_ids
                   if rid in self.shared_resources]

    def search_resources(self, query: str = None, resource_type: str = None,
                        subject: str = None, grade_level: str = None,
                        tags: List[str] = None, is_public: bool = None,
                        limit: int = 20) -> List[SharedResource]:
        """Search shared resources with filters."""
        with self._lock:
            results = []
            for resource in self.shared_resources.values():
                # Apply filters
                if is_public is not None and resource.is_public != is_public:
                    continue
                if resource_type and resource.resource_type.value != resource_type:
                    continue
                if subject and resource.subject != subject:
                    continue
                if grade_level and resource.grade_level != grade_level:
                    continue
                if tags and not any(tag in resource.tags for tag in tags):
                    continue
                if query:
                    query_lower = query.lower()
                    if not (query_lower in resource.title.lower() or
                           query_lower in resource.description.lower()):
                        continue

                results.append(resource)
                if len(results) >= limit:
                    break

            return results

    def increment_resource_view(self, resource_id: str) -> bool:
        """Increment view count for a resource."""
        with self._lock:
            try:
                if resource_id in self.shared_resources:
                    self.shared_resources[resource_id].increment_view_count()
                    self._save_to_file()
                    return True
                return False
            except Exception as e:
                print(f"Error incrementing resource view: {e}")
                return False

    def increment_resource_download(self, resource_id: str) -> bool:
        """Increment download count for a resource."""
        with self._lock:
            try:
                if resource_id in self.shared_resources:
                    self.shared_resources[resource_id].increment_download_count()
                    self._save_to_file()
                    return True
                return False
            except Exception as e:
                print(f"Error incrementing resource download: {e}")
                return False

    # Collaborative Session Operations
    def save_collaborative_session(self, session: CollaborativeSession) -> bool:
        """Save a collaborative session."""
        with self._lock:
            try:
                self.collaborative_sessions[session.session_id] = session
                self._save_to_file()
                return True
            except Exception as e:
                print(f"Error saving collaborative session: {e}")
                return False

    def get_collaborative_session(self, session_id: str) -> Optional[CollaborativeSession]:
        """Get collaborative session by ID."""
        with self._lock:
            return self.collaborative_sessions.get(session_id)

    def get_user_collaborative_sessions(self, user_id: str,
                                       active_only: bool = False) -> List[CollaborativeSession]:
        """Get collaborative sessions for a user."""
        with self._lock:
            sessions = []
            for session in self.collaborative_sessions.values():
                if (user_id == session.creator_id or user_id in session.participants):
                    if not active_only or session.is_active:
                        sessions.append(session)
            return sessions

    def get_active_collaborative_sessions(self) -> List[CollaborativeSession]:
        """Get all active collaborative sessions."""
        with self._lock:
            return [session for session in self.collaborative_sessions.values()
                   if session.is_active]

    def update_session_content(self, session_id: str, content_data: Dict[str, Any],
                              modified_by: str) -> bool:
        """Update collaborative session content."""
        with self._lock:
            try:
                if session_id in self.collaborative_sessions:
                    session = self.collaborative_sessions[session_id]
                    session.update_content(content_data, modified_by)
                    self._save_to_file()
                    return True
                return False
            except Exception as e:
                print(f"Error updating session content: {e}")
                return False

    def add_session_participant(self, session_id: str, user_id: str) -> bool:
        """Add a participant to a collaborative session."""
        with self._lock:
            try:
                if session_id in self.collaborative_sessions:
                    session = self.collaborative_sessions[session_id]
                    if session.add_participant(user_id):
                        self._save_to_file()
                        return True
                return False
            except Exception as e:
                print(f"Error adding session participant: {e}")
                return False

    def remove_session_participant(self, session_id: str, user_id: str) -> bool:
        """Remove a participant from a collaborative session."""
        with self._lock:
            try:
                if session_id in self.collaborative_sessions:
                    session = self.collaborative_sessions[session_id]
                    if session.remove_participant(user_id):
                        self._save_to_file()
                        return True
                return False
            except Exception as e:
                print(f"Error removing session participant: {e}")
                return False

    # Enhanced Storage Statistics
    def get_collaboration_stats(self) -> Dict[str, Any]:
        """Get collaboration-specific storage statistics."""
        with self._lock:
            total_messages = sum(len(messages) for messages in self.chat_messages.values())
            total_dm = sum(len(messages) for messages in self.direct_messages.values())
            active_sessions = len([s for s in self.collaborative_sessions.values() if s.is_active])

            return {
                'users_count': len(self.users),
                'study_groups_count': len(self.study_groups),
                'total_group_messages': total_messages,
                'total_direct_messages': total_dm,
                'shared_resources_count': len(self.shared_resources),
                'collaborative_sessions_count': len(self.collaborative_sessions),
                'active_collaborative_sessions': active_sessions,
                'last_updated': datetime.utcnow().isoformat()
            }

    def export_collaboration_data(self, format: str = 'json') -> Union[str, bytes]:
        """Export collaboration data in specified format."""
        with self._lock:
            data = {
                'users': {uid: user.to_dict() for uid, user in self.users.items()},
                'study_groups': {gid: group.to_dict() for gid, group in self.study_groups.items()},
                'chat_messages': {
                    gid: [msg.to_dict() for msg in messages]
                    for gid, messages in self.chat_messages.items()
                },
                'direct_messages': {
                    cid: [msg.to_dict() for msg in messages]
                    for cid, messages in self.direct_messages.items()
                },
                'shared_resources': {
                    rid: resource.to_dict()
                    for rid, resource in self.shared_resources.items()
                },
                'collaborative_sessions': {
                    sid: session.to_dict()
                    for sid, session in self.collaborative_sessions.items()
                },
                'exported_at': datetime.utcnow().isoformat()
            }

            if format.lower() == 'json':
                return json.dumps(data, indent=2)
            elif format.lower() == 'pickle':
                return pickle.dumps(data)
            else:
                raise ValueError(f"Unsupported export format: {format}")
