"""
Performance metrics data models for TutorX-MCP.

This module defines data structures for tracking and analyzing
student performance across different concepts and sessions.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import json


class MetricType(Enum):
    """Types of performance metrics."""
    ACCURACY = "accuracy"
    SPEED = "speed"
    ENGAGEMENT = "engagement"
    RETENTION = "retention"
    DIFFICULTY_PROGRESSION = "difficulty_progression"


@dataclass
class ConceptMetrics:
    """Performance metrics for a specific concept."""
    concept_id: str
    student_id: str
    
    # Basic performance metrics
    attempts: int = 0
    correct_attempts: int = 0
    total_time_spent: int = 0  # seconds
    average_response_time: float = 0.0  # seconds
    
    # Accuracy metrics
    accuracy_rate: float = 0.0
    accuracy_trend: List[float] = field(default_factory=list)  # Recent accuracy scores
    
    # Difficulty progression
    current_difficulty: float = 0.5
    max_difficulty_reached: float = 0.5
    difficulty_progression: List[float] = field(default_factory=list)
    
    # Learning patterns
    learning_velocity: float = 0.0  # improvement rate
    retention_score: float = 0.0
    engagement_score: float = 0.0
    
    # Timestamps
    first_attempt: Optional[datetime] = None
    last_attempt: Optional[datetime] = None
    last_updated: datetime = field(default_factory=datetime.utcnow)
    
    # Mastery tracking
    mastery_level: float = 0.0  # 0.0 to 1.0
    is_mastered: bool = False
    mastery_achieved_at: Optional[datetime] = None
    
    def update_attempt(self, is_correct: bool, response_time: float, difficulty: float):
        """Update metrics with a new attempt."""
        self.attempts += 1
        if is_correct:
            self.correct_attempts += 1
        
        self.total_time_spent += int(response_time)
        self.average_response_time = self.total_time_spent / self.attempts
        
        # Update accuracy
        self.accuracy_rate = self.correct_attempts / self.attempts
        self.accuracy_trend.append(self.accuracy_rate)
        if len(self.accuracy_trend) > 10:  # Keep last 10 attempts
            self.accuracy_trend.pop(0)
        
        # Update difficulty tracking
        self.current_difficulty = difficulty
        if difficulty > self.max_difficulty_reached:
            self.max_difficulty_reached = difficulty
        self.difficulty_progression.append(difficulty)
        
        # Update timestamps
        if self.first_attempt is None:
            self.first_attempt = datetime.utcnow()
        self.last_attempt = datetime.utcnow()
        self.last_updated = datetime.utcnow()
        
        # Calculate mastery level
        self._calculate_mastery()
    
    def _calculate_mastery(self):
        """Calculate mastery level based on performance metrics."""
        if self.attempts < 3:
            self.mastery_level = 0.0
            return
        
        # Weighted combination of accuracy, consistency, and difficulty
        accuracy_weight = 0.4
        consistency_weight = 0.3
        difficulty_weight = 0.3
        
        # Accuracy component
        accuracy_component = self.accuracy_rate
        
        # Consistency component (based on recent accuracy trend)
        if len(self.accuracy_trend) >= 3:
            recent_accuracy = self.accuracy_trend[-3:]
            consistency_component = 1.0 - (max(recent_accuracy) - min(recent_accuracy))
        else:
            consistency_component = 0.5
        
        # Difficulty component
        difficulty_component = min(self.current_difficulty * 2, 1.0)
        
        self.mastery_level = (
            accuracy_weight * accuracy_component +
            consistency_weight * consistency_component +
            difficulty_weight * difficulty_component
        )
        
        # Check if mastered (threshold: 0.8)
        if self.mastery_level >= 0.8 and not self.is_mastered:
            self.is_mastered = True
            self.mastery_achieved_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'concept_id': self.concept_id,
            'student_id': self.student_id,
            'attempts': self.attempts,
            'correct_attempts': self.correct_attempts,
            'total_time_spent': self.total_time_spent,
            'average_response_time': self.average_response_time,
            'accuracy_rate': self.accuracy_rate,
            'accuracy_trend': self.accuracy_trend,
            'current_difficulty': self.current_difficulty,
            'max_difficulty_reached': self.max_difficulty_reached,
            'difficulty_progression': self.difficulty_progression,
            'learning_velocity': self.learning_velocity,
            'retention_score': self.retention_score,
            'engagement_score': self.engagement_score,
            'first_attempt': self.first_attempt.isoformat() if self.first_attempt else None,
            'last_attempt': self.last_attempt.isoformat() if self.last_attempt else None,
            'last_updated': self.last_updated.isoformat(),
            'mastery_level': self.mastery_level,
            'is_mastered': self.is_mastered,
            'mastery_achieved_at': self.mastery_achieved_at.isoformat() if self.mastery_achieved_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConceptMetrics':
        """Create ConceptMetrics from dictionary."""
        first_attempt = datetime.fromisoformat(data['first_attempt']) if data.get('first_attempt') else None
        last_attempt = datetime.fromisoformat(data['last_attempt']) if data.get('last_attempt') else None
        last_updated = datetime.fromisoformat(data['last_updated']) if data.get('last_updated') else datetime.utcnow()
        mastery_achieved_at = datetime.fromisoformat(data['mastery_achieved_at']) if data.get('mastery_achieved_at') else None
        
        return cls(
            concept_id=data['concept_id'],
            student_id=data['student_id'],
            attempts=data.get('attempts', 0),
            correct_attempts=data.get('correct_attempts', 0),
            total_time_spent=data.get('total_time_spent', 0),
            average_response_time=data.get('average_response_time', 0.0),
            accuracy_rate=data.get('accuracy_rate', 0.0),
            accuracy_trend=data.get('accuracy_trend', []),
            current_difficulty=data.get('current_difficulty', 0.5),
            max_difficulty_reached=data.get('max_difficulty_reached', 0.5),
            difficulty_progression=data.get('difficulty_progression', []),
            learning_velocity=data.get('learning_velocity', 0.0),
            retention_score=data.get('retention_score', 0.0),
            engagement_score=data.get('engagement_score', 0.0),
            first_attempt=first_attempt,
            last_attempt=last_attempt,
            last_updated=last_updated,
            mastery_level=data.get('mastery_level', 0.0),
            is_mastered=data.get('is_mastered', False),
            mastery_achieved_at=mastery_achieved_at
        )


@dataclass
class SessionMetrics:
    """Performance metrics for a learning session."""
    session_id: str
    student_id: str
    
    # Session details
    start_time: datetime = field(default_factory=datetime.utcnow)
    end_time: Optional[datetime] = None
    duration: int = 0  # seconds
    
    # Activity metrics
    concepts_attempted: List[str] = field(default_factory=list)
    questions_answered: int = 0
    correct_answers: int = 0
    hints_used: int = 0
    
    # Engagement metrics
    interaction_count: int = 0
    idle_time: int = 0  # seconds
    engagement_score: float = 0.0
    
    # Performance metrics
    average_accuracy: float = 0.0
    average_response_time: float = 0.0
    difficulty_progression: List[float] = field(default_factory=list)
    
    def end_session(self):
        """End the session and calculate final metrics."""
        self.end_time = datetime.utcnow()
        self.duration = int((self.end_time - self.start_time).total_seconds())
        
        # Calculate final metrics
        if self.questions_answered > 0:
            self.average_accuracy = self.correct_answers / self.questions_answered
        
        # Calculate engagement score
        if self.duration > 0:
            active_time = self.duration - self.idle_time
            self.engagement_score = min(active_time / self.duration, 1.0)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'session_id': self.session_id,
            'student_id': self.student_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration': self.duration,
            'concepts_attempted': self.concepts_attempted,
            'questions_answered': self.questions_answered,
            'correct_answers': self.correct_answers,
            'hints_used': self.hints_used,
            'interaction_count': self.interaction_count,
            'idle_time': self.idle_time,
            'engagement_score': self.engagement_score,
            'average_accuracy': self.average_accuracy,
            'average_response_time': self.average_response_time,
            'difficulty_progression': self.difficulty_progression
        }


@dataclass
class PerformanceMetrics:
    """Overall performance metrics for a student."""
    student_id: str
    
    # Overall statistics
    total_sessions: int = 0
    total_learning_time: int = 0  # seconds
    total_concepts_attempted: int = 0
    total_concepts_mastered: int = 0
    
    # Performance averages
    overall_accuracy: float = 0.0
    average_session_duration: float = 0.0
    average_engagement: float = 0.0
    
    # Progress tracking
    concepts_metrics: Dict[str, ConceptMetrics] = field(default_factory=dict)
    recent_sessions: List[SessionMetrics] = field(default_factory=list)
    
    # Timestamps
    last_updated: datetime = field(default_factory=datetime.utcnow)
    
    def update_with_session(self, session_metrics: SessionMetrics):
        """Update overall metrics with a new session."""
        self.total_sessions += 1
        self.total_learning_time += session_metrics.duration
        
        # Update recent sessions (keep last 10)
        self.recent_sessions.append(session_metrics)
        if len(self.recent_sessions) > 10:
            self.recent_sessions.pop(0)
        
        # Recalculate averages
        if self.total_sessions > 0:
            self.average_session_duration = self.total_learning_time / self.total_sessions
            
            # Calculate overall accuracy from recent sessions
            if self.recent_sessions:
                total_questions = sum(s.questions_answered for s in self.recent_sessions)
                total_correct = sum(s.correct_answers for s in self.recent_sessions)
                if total_questions > 0:
                    self.overall_accuracy = total_correct / total_questions
                
                # Calculate average engagement
                self.average_engagement = sum(s.engagement_score for s in self.recent_sessions) / len(self.recent_sessions)
        
        self.last_updated = datetime.utcnow()
    
    def get_concept_metrics(self, concept_id: str) -> ConceptMetrics:
        """Get or create concept metrics."""
        if concept_id not in self.concepts_metrics:
            self.concepts_metrics[concept_id] = ConceptMetrics(
                concept_id=concept_id,
                student_id=self.student_id
            )
        return self.concepts_metrics[concept_id]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'student_id': self.student_id,
            'total_sessions': self.total_sessions,
            'total_learning_time': self.total_learning_time,
            'total_concepts_attempted': self.total_concepts_attempted,
            'total_concepts_mastered': self.total_concepts_mastered,
            'overall_accuracy': self.overall_accuracy,
            'average_session_duration': self.average_session_duration,
            'average_engagement': self.average_engagement,
            'concepts_metrics': {k: v.to_dict() for k, v in self.concepts_metrics.items()},
            'recent_sessions': [s.to_dict() for s in self.recent_sessions],
            'last_updated': self.last_updated.isoformat()
        }
